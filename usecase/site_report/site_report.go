package sitereport

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	basicpriceDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/basic_price"
	blockDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/block"
	dailyreportadditionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/daily_report_addition"
	departmentpicDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/department_pic"
	distantfeeDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/distant_fee"
	incometaxDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/income_tax"
	optionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/option"
	qualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/qualification"
	sitereportDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report"
	sitereportdailyreportadditionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_daily_report_addition"
	sitereportoptionDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_option"
	sitereportstatutoryDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_statutory"
	sitereportworkerDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_worker"
	sitereportworkerqualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_worker_qualification"
	statutoryDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/statutory"
	dbLib "github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/database"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/parse"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/utils"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
)

func (uc *SiteReportUseCase) GetList(ctx context.Context, req GetListReq) ([]GetListResp, error) {
	// Get site reports from domain
	param := sitereportDmn.GetListParam{
		StartDate: req.ParsedStartDate,
		EndDate:   req.ParsedEndDate,
	}

	reports, err := uc.sitereport.GetList(ctx, param)
	if err != nil {
		return []GetListResp{}, log.LogError(err, nil)
	}
	if len(reports) == 0 {
		return []GetListResp{}, nil
	}

	// Aggregate data hierarchically
	return aggregateReports(reports), nil
}

func (uc *SiteReportUseCase) GetInvoiceList(ctx context.Context, req GetInvoiceListReq) (GetInvoiceListResp, error) {
	// Get site reports from domain
	reports, err := uc.sitereport.GetInvoiceList(ctx, sitereportDmn.GetInvoiceListParam{
		StartDate: req.ParsedStartDate,
		EndDate:   req.ParsedEndDate,
	})
	if err != nil {
		return GetInvoiceListResp{}, log.LogError(err, nil)
	}
	if len(reports) == 0 {
		return GetInvoiceListResp{
			TotalBillingAmount: 0,
			TotalInvoice:       0,
			Customer:           []GetInvoiceListCustomerResp{},
		}, nil
	}

	// Group by customer and calculate totals in Go code
	return aggregateInvoiceReports(reports), nil
}

func (uc *SiteReportUseCase) GetStatutoryList(ctx context.Context, req GetStatutoryListReq) (GetStatutoryListResp, error) {
	// Get site reports from domain
	reports, err := uc.sitereport.GetStatutoryList(ctx, sitereportDmn.GetStatutoryListParam{
		StartDate: req.ParsedStartDate,
		EndDate:   req.ParsedEndDate,
	})
	if err != nil {
		return GetStatutoryListResp{}, log.LogError(err, nil)
	}
	if len(reports) == 0 {
		return GetStatutoryListResp{
			TotalStatutoryAmount: 0,
			TotalStatutoryReport: 0,
			Customer:             []GetStatutoryListCustomerResp{},
		}, nil
	}

	// Group by customer and calculate totals in Go code
	return aggregateStatutoryReports(reports), nil
}

func (uc *SiteReportUseCase) GetWorkerReportList(ctx context.Context, req GetWorkerReportListReq) (GetWorkerReportListResp, error) {
	// Get worker reports from domain
	workers, err := uc.sitereportworker.GetWorkerReportList(ctx, sitereportworkerDmn.GetWorkerReportListParam{
		StartDate: req.ParsedStartDate,
		EndDate:   req.ParsedEndDate,
	})
	if err != nil {
		return GetWorkerReportListResp{}, log.LogError(err, nil)
	}
	if len(workers) == 0 {
		return GetWorkerReportListResp{}, nil
	}

	// Group by worker and aggregate reports in Go code
	return aggregateWorkerReports(workers), nil
}

func (uc *SiteReportUseCase) GetWorkerPaymentStatementList(ctx context.Context, req GetWorkerPaymentStatementListReq) (GetWorkerPaymentStatementListResp, error) {
	// Get worker payment statements from domain
	workers, err := uc.sitereportworker.GetWorkerPaymentStatementList(ctx, sitereportworkerDmn.GetWorkerPaymentStatementListParam{
		StartDate: req.ParsedStartDate,
		EndDate:   req.ParsedEndDate,
	})
	if err != nil {
		return GetWorkerPaymentStatementListResp{}, log.LogError(err, nil)
	}
	if len(workers) == 0 {
		return GetWorkerPaymentStatementListResp{
			TotalPaymentAmount:    0,
			TotalPaymentStatement: 0,
			PaymentStatement:      []GetWorkerPaymentStatementListItemResp{},
		}, nil
	}

	// Process and aggregate payment statement data
	return aggregateWorkerPaymentStatements(workers), nil
}

// aggregateReports organizes site reports into hierarchical structure by year -> month -> date
func aggregateReports(reports []sitereportDmn.SiteReport) []GetListResp {
	// Group by year -> month -> date
	yearMap := make(map[int]map[int]map[int][]sitereportDmn.SiteReport)

	for _, report := range reports {
		year := report.WorkDate.Year()
		month := int(report.WorkDate.Month())
		day := report.WorkDate.Day()

		if yearMap[year] == nil {
			yearMap[year] = make(map[int]map[int][]sitereportDmn.SiteReport)
		}
		if yearMap[year][month] == nil {
			yearMap[year][month] = make(map[int][]sitereportDmn.SiteReport)
		}
		if yearMap[year][month][day] == nil {
			yearMap[year][month][day] = []sitereportDmn.SiteReport{}
		}

		yearMap[year][month][day] = append(yearMap[year][month][day], report)
	}

	// Convert to response structure with proper sorting
	var result []GetListResp

	// Sort years
	var years []int
	for year := range yearMap {
		years = append(years, year)
	}
	sortInts(years)

	for _, year := range years {
		monthMap := yearMap[year]
		yearResp := GetListResp{
			Year:  fmt.Sprintf("%d年", year),
			Month: []MonthData{},
		}

		// Sort months
		var months []int
		for month := range monthMap {
			months = append(months, month)
		}
		sortInts(months)

		for _, month := range months {
			dayMap := monthMap[month]
			monthData := MonthData{
				Value:       fmt.Sprintf("%02d月", month),
				Worker:      0,
				TotalAmount: 0,
				Date:        []DateData{},
			}

			// Sort days
			var days []int
			for day := range dayMap {
				days = append(days, day)
			}
			sortInts(days)

			for _, day := range days {
				dayReports := dayMap[day]
				dateData := DateData{
					Value:       fmt.Sprintf("%d日", day),
					Worker:      0,
					TotalAmount: 0,
					Report:      []ReportData{},
				}

				// Process reports for this date
				for _, report := range dayReports {
					reportData := ReportData{
						SiteReportID: report.ID,
						SiteName:     report.SiteName,
						Worker:       report.Worker,
						TotalAmount:  report.TotalAmount,
						BStartTime:   report.BStartTime.Format("15:04"),
						BEndTime:     report.BEndTime.Format("15:04"),
						Note:         report.Note,
						IsLocked:     report.IsLocked,
					}

					dateData.Report = append(dateData.Report, reportData)
					dateData.Worker += report.Worker
					dateData.TotalAmount += report.TotalAmount
				}

				monthData.Date = append(monthData.Date, dateData)
				monthData.Worker += dateData.Worker
				monthData.TotalAmount += dateData.TotalAmount
			}

			yearResp.Month = append(yearResp.Month, monthData)
		}

		result = append(result, yearResp)
	}

	return result
}

// sortInts sorts a slice of integers in ascending order
func sortInts(slice []int) {
	for i := 0; i < len(slice)-1; i++ {
		for j := i + 1; j < len(slice); j++ {
			if slice[i] > slice[j] {
				slice[i], slice[j] = slice[j], slice[i]
			}
		}
	}
}

// aggregateInvoiceReports groups site reports by customer and calculates totals
func aggregateInvoiceReports(reports []sitereportDmn.SiteReport) GetInvoiceListResp {
	// Group by customer name
	customerMap := make(map[string][]sitereportDmn.SiteReport)

	// Calculate totals
	var totalBillingAmount float64
	totalInvoice := len(reports)

	for _, report := range reports {
		// Calculate total billing amount
		totalBillingAmount += report.TotalAmount

		// Get customer name from the relationship chain
		var customerName string
		if report.DepartmentPic.Department.Customer.ID != 0 {
			customerName = report.DepartmentPic.Department.Customer.Name
		} else {
			customerName = "Unknown Customer"
		}

		// Group by customer
		if customerMap[customerName] == nil {
			customerMap[customerName] = []sitereportDmn.SiteReport{}
		}
		customerMap[customerName] = append(customerMap[customerName], report)
	}

	// Convert to response structure
	var customers []GetInvoiceListCustomerResp

	// Sort customer names for consistent output
	var customerNames []string
	for name := range customerMap {
		customerNames = append(customerNames, name)
	}
	utils.SortStrings(customerNames)

	for _, customerName := range customerNames {
		customerReports := customerMap[customerName]

		var invoices []GetInvoiceListInvoiceResp
		for _, report := range customerReports {
			invoice := GetInvoiceListInvoiceResp{
				SiteReportID:    report.ID,
				DepartmentName:  getDepartmentName(report),
				PicName:         getPicName(report),
				WorkDate:        report.WorkDate.Format(time.DateOnly),
				BillDate:        report.BillDate.Format(time.DateOnly),
				SiteName:        report.SiteName,
				TotalWorker:     report.Worker,
				BStartTime:      report.BStartTime.Format(time.TimeOnly),
				BEndTime:        report.BEndTime.Format(time.TimeOnly),
				IsLocked:        report.IsLocked,
				IsInvoiceIssued: report.IsInvoiceIssued,
			}
			invoices = append(invoices, invoice)
		}

		customer := GetInvoiceListCustomerResp{
			Name:    customerName,
			Invoice: invoices,
		}
		customers = append(customers, customer)
	}

	return GetInvoiceListResp{
		TotalBillingAmount: totalBillingAmount,
		TotalInvoice:       totalInvoice,
		Customer:           customers,
	}
}

// aggregateStatutoryReports groups site reports by customer and calculates statutory totals
func aggregateStatutoryReports(reports []sitereportDmn.SiteReport) GetStatutoryListResp {
	// Group by customer name
	customerMap := make(map[string][]sitereportDmn.SiteReport)

	// Calculate totals
	var totalStatutoryAmount float64
	totalStatutoryReport := len(reports)

	for _, report := range reports {
		// Calculate total statutory amount from the first statutory record
		if len(report.Statutory) > 0 {
			totalStatutoryAmount += report.Statutory[0].TotalAmount
		}

		// Get customer name from the relationship chain
		var customerName string
		if report.DepartmentPic.Department.Customer.ID != 0 {
			customerName = report.DepartmentPic.Department.Customer.Name
		} else {
			customerName = "Unknown Customer"
		}

		// Group by customer
		if customerMap[customerName] == nil {
			customerMap[customerName] = []sitereportDmn.SiteReport{}
		}
		customerMap[customerName] = append(customerMap[customerName], report)
	}

	// Convert to response structure
	var customers []GetStatutoryListCustomerResp

	// Sort customer names for consistent output
	var customerNames []string
	for name := range customerMap {
		customerNames = append(customerNames, name)
	}
	utils.SortStrings(customerNames)

	for _, customerName := range customerNames {
		customerReports := customerMap[customerName]

		var statutoryList []GetStatutoryListStatutoryResp
		for _, report := range customerReports {
			var siteReportStatutoryID int64
			var isStatutoryIssued bool

			// Get statutory data from the first statutory record
			if len(report.Statutory) > 0 {
				siteReportStatutoryID = report.Statutory[0].ID
				isStatutoryIssued = report.Statutory[0].IsStatutoryIssued
			}

			statutory := GetStatutoryListStatutoryResp{
				SiteReportID:          report.ID,
				SiteReportStatutoryID: siteReportStatutoryID,
				DepartmentName:        getDepartmentName(report),
				PicName:               getPicName(report),
				WorkDate:              report.WorkDate.Format(time.DateOnly),
				BillDate:              report.BillDate.Format(time.DateOnly),
				SiteName:              report.SiteName,
				TotalWorker:           report.Worker,
				BStartTime:            report.BStartTime.Format(time.TimeOnly),
				BEndTime:              report.BEndTime.Format(time.TimeOnly),
				IsLocked:              report.IsLocked,
				IsStatutoryIssued:     isStatutoryIssued,
			}
			statutoryList = append(statutoryList, statutory)
		}

		customer := GetStatutoryListCustomerResp{
			Name:      customerName,
			Statutory: statutoryList,
		}
		customers = append(customers, customer)
	}

	return GetStatutoryListResp{
		TotalStatutoryAmount: totalStatutoryAmount,
		TotalStatutoryReport: totalStatutoryReport,
		Customer:             customers,
	}
}

// aggregateWorkerReports groups worker reports by worker name
func aggregateWorkerReports(workers []sitereportworkerDmn.WorkerReportListItem) GetWorkerReportListResp {
	// Group by worker name
	workerMap := make(map[string][]sitereportworkerDmn.WorkerReportListItem)

	for _, worker := range workers {
		workerName := worker.User.Name
		if workerMap[workerName] == nil {
			workerMap[workerName] = []sitereportworkerDmn.WorkerReportListItem{}
		}
		workerMap[workerName] = append(workerMap[workerName], worker)
	}

	// Convert to response structure
	var result GetWorkerReportListResp

	// Sort worker names for consistent output
	var workerNames []string
	for name := range workerMap {
		workerNames = append(workerNames, name)
	}
	utils.SortStrings(workerNames)

	for _, workerName := range workerNames {
		workerReports := workerMap[workerName]

		var reports []GetWorkerReportListReportResp
		for _, worker := range workerReports {
			// Format issued_date - handle nullable field
			issuedDate := ""
			if worker.IssuedDate != nil {
				issuedDate = worker.IssuedDate.Format(time.DateOnly)
			}

			report := GetWorkerReportListReportResp{
				SiteReportID:       worker.SiteReportID,
				SiteReportWorkerID: worker.ID,
				SiteName:           worker.SiteName,
				WorkDate:           worker.WorkDate.Format(time.DateOnly),
				StartTime:          worker.StartTime.Format(time.TimeOnly),
				EndTime:            worker.EndTime.Format(time.TimeOnly),
				Status:             worker.Status,
				IssuedDate:         issuedDate,
			}
			reports = append(reports, report)
		}

		workerResp := GetWorkerReportListWorkerResp{
			WorkerName: workerName,
			Report:     reports,
		}
		result = append(result, workerResp)
	}

	return result
}

// aggregateWorkerPaymentStatements processes worker payment statement data and calculates totals
func aggregateWorkerPaymentStatements(workers []sitereportworkerDmn.WorkerPaymentStatementListItem) GetWorkerPaymentStatementListResp {
	var totalPaymentAmount float64
	var paymentStatements []GetWorkerPaymentStatementListItemResp

	for _, worker := range workers {
		// Parse snapshot JSON to extract distant_fee
		var snapshot sitereportworkerDmn.Snapshot
		var taxFreeSalary float64

		if worker.Snapshot != "" {
			err := json.Unmarshal([]byte(worker.Snapshot), &snapshot)
			if err == nil {
				taxFreeSalary = snapshot.DistantFee
			}
		}

		// Calculate derived fields according to requirements
		totalPayment := worker.Amount
		taxAmount := worker.Tax
		debtPayment := totalPayment - taxAmount
		taxableSalary := totalPayment - taxFreeSalary

		// Add to total payment amount
		totalPaymentAmount += totalPayment

		// Create payment statement item
		paymentStatement := GetWorkerPaymentStatementListItemResp{
			SiteReportID:       worker.SiteReportID,
			SiteReportWorkerID: worker.SiteReportWorkerID,
			WorkerID:           worker.UserID,
			WorkerName:         worker.User.Name,
			WorkDate:           worker.WorkDate.Format(time.DateOnly),
			DebtPayment:        debtPayment,
			TaxableSalary:      taxableSalary,
			TaxFreeSalary:      taxFreeSalary,
			TaxAmount:          taxAmount,
			TotalPayment:       totalPayment,
		}
		paymentStatements = append(paymentStatements, paymentStatement)
	}

	return GetWorkerPaymentStatementListResp{
		TotalPaymentAmount:    totalPaymentAmount,
		TotalPaymentStatement: len(workers),
		PaymentStatement:      paymentStatements,
	}
}

// getDepartmentName extracts department name from site report relationships
func getDepartmentName(report sitereportDmn.SiteReport) string {
	if report.DepartmentPic.Department.ID != 0 {
		return report.DepartmentPic.Department.Name
	}
	return ""
}

// getPicName extracts PIC name from site report relationships
func getPicName(report sitereportDmn.SiteReport) string {
	if report.DepartmentPic.ID != 0 {
		return report.DepartmentPic.PicName
	}
	return ""
}

// BulkUpdate handles bulk updating of site reports with role-based authorization
func (uc *SiteReportUseCase) BulkUpdate(ctx context.Context, req BulkUpdateReq) error {
	// Convert request to domain parameter
	param := sitereportDmn.BulkUpdateParam{
		SiteReportIDs:   req.SiteReportIDs,
		IsLocked:        req.IsLocked,
		IsInvoiceIssued: req.IsInvoiceIssued,
	}

	// Parse work_date if provided
	if req.WorkDate != nil {
		reports, err := uc.sitereport.GetDetailList(ctx, sitereportDmn.GetDetailListParam{
			IDs: req.SiteReportIDs,
		})
		if err != nil {
			return log.LogError(err, nil)
		}

		for _, report := range reports {
			if report.IsLocked {
				return log.LogError(ErrCannotUpdateLockedReport, nil)
			}
		}

		workDate, err := time.Parse("2006-01-02", *req.WorkDate)
		if err != nil {
			return log.LogError(ErrInvalidDateFormat, nil)
		}
		param.WorkDate = &workDate
	}

	// Call domain layer
	err := uc.sitereport.BulkUpdate(ctx, param)
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// StatutoryBulkUpdate updates the is_statutory_issued status for multiple site report statutory records
func (uc *SiteReportUseCase) StatutoryBulkUpdate(ctx context.Context, req BulkUpdateStatutoryReq) error {
	// Call domain layer
	err := uc.sitereportstatutory.BulkUpdateStatutory(ctx, sitereportstatutoryDmn.BulkUpdateStatutoryParam{
		SiteReportStatutoryIDs: req.SiteReportStatutoryIDs,
		IsStatutoryIssued:      req.IsStatutoryIssued,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// WorkerBulkUpdate updates the payment status and issued_date for multiple site report worker records
func (uc *SiteReportUseCase) WorkerBulkUpdate(ctx context.Context, req BulkUpdateWorkerReq) error {
	// Call domain layer
	err := uc.sitereportworker.BulkUpdateWorkerPaymentStatus(ctx, sitereportworkerDmn.BulkUpdateWorkerPaymentStatusParam{
		SiteReportWorkerIDs: req.SiteReportWorkerIDs,
		IsPaid:              req.IsPaid,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// GetStatutoryCalculationVariable calculates statutory calculation variables
func (uc *SiteReportUseCase) GetStatutoryCalculationVariable(ctx context.Context, req GetStatutoryCalculationVariableReq) (GetStatutoryCalculationVariableResp, error) {
	var resp GetStatutoryCalculationVariableResp

	// Run calculations concurrently using errgroup for efficiency
	var (
		statutoryRate        float64
		expensePerWorkerResp CalculateExpensePerWorkerResp
		addFeePerSite        float64
		addFeePerWorker      float64
	)

	g, egCtx := errgroup.WithContext(ctx)

	// Calculate statutory_rate
	g.Go(func() error {
		var err error
		statutoryRate, err = uc.calculateStatutoryRate(egCtx, req.CustomerID)
		return err
	})

	// Calculate expense_per_worker
	g.Go(func() error {
		var err error
		expensePerWorkerResp, err = uc.calculateExpensePerWorker(egCtx, CalculateExpensePerWorker{
			BasicPriceID: req.BasicPriceID,
			StartTime:    req.ParsedStartTime,
			EndTime:      req.ParsedEndTime,
			BreakTime:    req.ParsedBreakTime,
		})
		return err
	})

	// Calculate add_fee_per_site and add_fee_per_worker
	g.Go(func() error {
		var err error
		addFeeResp, err := uc.calculateAddFees(egCtx, req.DailyReportAdditionIDs)
		if err != nil {
			return err
		}
		addFeePerSite = addFeeResp.AddFeePerSite
		addFeePerWorker = addFeeResp.AddFeePerWorker
		return nil
	})

	// Wait for all calculations to complete
	if err := g.Wait(); err != nil {
		return GetStatutoryCalculationVariableResp{}, log.LogError(err, nil)
	}

	// Assign results to response
	resp.StatutoryRate = statutoryRate
	resp.ExpensePerWorker = expensePerWorkerResp.TotalExpense
	resp.AddFeePerSite = addFeePerSite
	resp.AddFeePerWorker = addFeePerWorker

	return resp, nil
}

// WorkerCalculation calculates worker payment including all allowances and taxes
func (uc *SiteReportUseCase) WorkerCalculation(ctx context.Context, req WorkerCalculationParam) (WorkerCalculationResp, error) {
	resp, err := uc.DoWorkerCalculation(ctx, req)
	if err != nil {
		return WorkerCalculationResp{}, log.LogError(err, nil)
	}

	return WorkerCalculationResp{
		TotalAmount:         resp.TotalAmount,
		TaxAmount:           resp.TaxAmount,
		TotalAmountAfterTax: resp.TotalAmountAfterTax,
	}, nil
}

func (uc *SiteReportUseCase) DoWorkerCalculation(ctx context.Context, param WorkerCalculationParam) (DoWorkerCalculationResp, error) {
	var (
		resp                 DoWorkerCalculationResp
		expensePerWorkerResp CalculateExpensePerWorkerResp
		distantFee           float64
		qualificationFeeResp CalculateQualificationFeeResp
	)

	// Use errgroup to perform calculations concurrently
	g, gctx := errgroup.WithContext(ctx)

	// Calculate base salary
	g.Go(func() error {
		var err error
		expensePerWorkerResp, err = uc.calculateExpensePerWorker(gctx, CalculateExpensePerWorker{
			BasicPriceID: param.BasicPriceID,
			StartTime:    param.ParsedStartTime,
			EndTime:      param.ParsedEndTime,
			BreakTime:    param.ParsedBreakTime,
		})
		return err
	})

	// Calculate distant fee if provided
	g.Go(func() error {
		var err error
		distantFee, err = uc.calculateDistantFee(gctx, param.DistantFeeID, param.ParsedStartTime, param.ParsedEndTime, param.ParsedBreakTime)
		return err
	})

	// Calculate qualification fees if provided
	g.Go(func() error {
		var err error
		qualificationFeeResp, err = uc.calculateQualificationFee(gctx, param.QualificationAllowanceIDs)
		if err != nil {
			return err
		}
		return err
	})

	// Wait for all calculations to complete
	if err := g.Wait(); err != nil {
		return DoWorkerCalculationResp{}, log.LogError(err, nil)
	}

	// Calculate total amount
	totalAmountTaxable := expensePerWorkerResp.TotalExpense + param.TransportExpense + param.LeaderAllowance + qualificationFeeResp.TotalFee
	totalAmount := totalAmountTaxable + distantFee

	// Calculate tax amount and get income tax ID
	taxAmountResp, err := uc.calculateTaxAmount(ctx, CalculateTaxAmountParam{
		TotalAmount: totalAmountTaxable,
		WorkerID:    param.WorkerID,
	})
	if err != nil {
		return DoWorkerCalculationResp{}, log.LogError(err, nil)
	}

	// Calculate total amount after tax
	totalAmountAfterTax := totalAmount - taxAmountResp.Amount

	// Assign results to response
	resp.TotalAmount = totalAmount
	resp.TaxIncomeID = taxAmountResp.IncomeTaxID
	resp.TaxAmount = taxAmountResp.Amount
	resp.TotalAmountAfterTax = totalAmountAfterTax
	resp.DistantFee = distantFee
	resp.Qualifications = qualificationFeeResp.Qualifications

	return resp, nil
}

// calculateDistantFee calculates distant fee based on working hours
func (uc *SiteReportUseCase) calculateDistantFee(ctx context.Context, distantFeeID int64, startTime, endTime time.Time, breakTime *time.Time) (float64, error) {
	if distantFeeID <= 0 {
		return 0, nil
	}

	// Get distant fee record
	distantFee, err := uc.distantfee.GetByID(ctx, distantfeeDmn.GetByIDParam{
		ID: distantFeeID,
	})
	if err != nil {
		return 0, err
	}
	if distantFee.ID == 0 {
		return 0, errors.New("distant fee not found")
	}

	// Calculate working hours
	workingHours := uc.calculateWorkingHours(startTime, endTime, breakTime)

	// Calculate distant fee: unit * add_amount_per_hour * working_hours
	distantFeeAmount := distantFee.Unit * distantFee.AddAmountPerHour * workingHours

	return distantFeeAmount, nil
}

// calculateQualificationFee calculates total qualification fees
func (uc *SiteReportUseCase) calculateQualificationFee(ctx context.Context, qualificationIDs []int64) (CalculateQualificationFeeResp, error) {
	if len(qualificationIDs) == 0 {
		return CalculateQualificationFeeResp{}, nil
	}

	// Get qualification records
	qualifications, err := uc.qualification.GetByIDs(ctx, qualificationDmn.GetByIDsParam{
		IDs: qualificationIDs,
	})
	if err != nil {
		return CalculateQualificationFeeResp{}, err
	}

	// Sum all add_claim values and store in Qualification struct
	var totalFee float64
	qualificationRes := make([]Qualification, len(qualifications))
	for i, qualification := range qualifications {
		totalFee += qualification.AddClaim

		qualificationRes[i] = Qualification{
			ID:       qualification.ID,
			Title:    qualification.Title,
			AddClaim: qualification.AddClaim,
		}
	}

	return CalculateQualificationFeeResp{
		TotalFee:       totalFee,
		Qualifications: qualificationRes,
	}, nil
}

// calculateTaxAmount calculates tax amount based on total amount and user dependency type
func (uc *SiteReportUseCase) calculateTaxAmount(ctx context.Context, param CalculateTaxAmountParam) (CalculateTaxAmountResp, error) {
	// Get user information to determine dependency type
	user, err := uc.user.GetByID(ctx, param.WorkerID)
	if err != nil {
		return CalculateTaxAmountResp{}, err
	}
	if user.ID == 0 {
		return CalculateTaxAmountResp{}, log.LogError(fmt.Errorf("user not found with ID: %d", param.WorkerID), nil)
	}

	// Return error if no dependency type is set
	if user.Dependent == nil || *user.Dependent == "" {
		return CalculateTaxAmountResp{}, log.LogError(fmt.Errorf("user dependency type not found with ID: %d", param.WorkerID), nil)
	}
	dependentType := *user.Dependent

	// Get income tax record
	incomeTax, err := uc.incomeTax.GetByAmount(ctx, incometaxDmn.GetByAmountParam{
		Amount: param.TotalAmount,
	})
	if err != nil {
		return CalculateTaxAmountResp{}, err
	}
	if incomeTax.ID == 0 {
		// No tax applicable
		return CalculateTaxAmountResp{}, nil
	}

	// Get the specific tax amount for the user's dependency type
	taxAmount, err := getTaxAmountByType(incomeTax, dependentType)
	if err != nil {
		return CalculateTaxAmountResp{}, err
	}

	return CalculateTaxAmountResp{
		IncomeTaxID: incomeTax.ID,
		Amount:      taxAmount,
	}, nil
}

// GetTaxAmountByType extracts the tax amount for a specific dependency type from the JSON amount field.
func getTaxAmountByType(incomeTax incometaxDmn.IncomeTax, dependentType string) (float64, error) {
	if incomeTax.Amount == "" {
		return 0, nil
	}

	var taxAmounts []incometaxDmn.TaxAmountItem
	err := json.Unmarshal([]byte(incomeTax.Amount), &taxAmounts)
	if err != nil {
		return 0, log.LogError(err, nil)
	}

	// Find the tax amount for the specified dependency type
	for _, item := range taxAmounts {
		if item.Type == dependentType {
			return item.Price, nil
		}
	}

	// If dependency type not found, return 0
	return 0, nil
}

// SaveWorker handles saving site report worker data with business logic
func (uc *SiteReportUseCase) SaveWorker(ctx context.Context, req SaveWorkerReq) error {
	tx := dbLib.InitTx()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Determine operation based on ID
	if req.ID < 0 {
		// Delete operation
		err := uc.deleteWorker(ctx, tx, -req.ID)
		if err != nil {
			tx.Rollback()
			return log.LogError(err, nil)
		}
	} else if req.ID > 0 {
		// Update operation
		err := uc.updateWorker(ctx, tx, req)
		if err != nil {
			tx.Rollback()
			return log.LogError(err, nil)
		}
	}

	// Invalid ID
	return tx.Commit().Error
}

// deleteWorker handles deleting a site report worker record
func (uc *SiteReportUseCase) deleteWorker(ctx context.Context, tx *gorm.DB, workerID int64) error {
	// Delete worker qualifications first
	err := uc.sitereportworkerqualification.BulkDeleteWithTx(ctx, tx, sitereportworkerqualificationDmn.BulkDeleteParam{
		SiteReportWorkerID: workerID,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	// Delete the worker record
	err = uc.sitereportworker.DeleteWorkerWithTx(ctx, tx, sitereportworkerDmn.DeleteWorkerParam{
		ID: workerID,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// updateWorker handles updating a site report worker record with calculations and snapshots
func (uc *SiteReportUseCase) updateWorker(ctx context.Context, tx *gorm.DB, req SaveWorkerReq) error {
	// Get user information for snapshot
	user, err := uc.user.GetByID(ctx, req.WorkerID)
	if err != nil {
		return log.LogError(err, nil)
	}
	if user.ID == 0 {
		return ErrUserNotFound
	}

	// Perform worker calculation
	qualificationAllowanceIDs := make([]int64, len(req.Qualifications))
	for i, qualification := range req.Qualifications {
		qualificationAllowanceIDs[i] = qualification.QualificationID
	}

	calcResp, err := uc.DoWorkerCalculation(ctx, WorkerCalculationParam{
		WorkerID:                  req.WorkerID,
		BasicPriceID:              req.BasicPriceID,
		StartTime:                 req.StartTime,
		EndTime:                   req.EndTime,
		BreakTime:                 req.BreakTime,
		TransportExpense:          req.TransportExpense,
		LeaderAllowance:           req.LeaderAllowance,
		DistantFeeID:              req.DistantFeeID,
		QualificationAllowanceIDs: qualificationAllowanceIDs,
		ParsedStartTime:           req.ParsedStartTime,
		ParsedEndTime:             req.ParsedEndTime,
		ParsedBreakTime:           req.ParsedBreakTime,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	// Create worker snapshot
	workerSnapshotJSON, err := json.Marshal(sitereportworkerDmn.Snapshot{
		WorkerName: user.Name,
		DistantFee: calcResp.DistantFee,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	// Do update worker
	err = uc.doUpdateWorker(ctx, tx, DoUpdateWorkerParam{
		Req:                req,
		CalcResp:           calcResp,
		WorkerSnapshotJSON: string(workerSnapshotJSON),
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// SaveSiteReport handles saving site report data with business logic
func (uc *SiteReportUseCase) SaveSiteReport(ctx context.Context, req SaveSiteReportReq) error {
	// Determine operation based on ID
	if req.ID < 0 {
		// Delete operation
		return uc.deleteSiteReport(ctx, -req.ID)
	} else if req.ID > 0 {
		// Update operation
		return uc.updateSiteReport(ctx, req)
	} else {
		// Create operation
		return uc.createSiteReport(ctx, req)
	}
}

// createSiteReport handles creating a new site report record
func (uc *SiteReportUseCase) createSiteReport(ctx context.Context, req SaveSiteReportReq) error {
	// Perform site report calculation to get total amount and snapshot data
	calcReq := SiteReportCalculationReq{
		HasStatutory:          req.HasStatutory,
		DepartmentPicID:       req.DepartmentPicID,
		BasicPriceID:          req.BasicPriceID,
		DailyReportAdditions:  req.DailyReportAdditions,
		TotalWorker:           req.TotalWorker,
		DistrictBlockID:       req.DistrictBlockID,
		DistrictBlockUnit:     req.DistrictBlockUnit,
		TransitPlaceBlockID:   req.TransitPlaceBlockID,
		TransitPlaceBlockUnit: req.TransitPlaceBlockUnit,
		BStartTime:            req.BStartTime,
		BEndTime:              req.BEndTime,
		BreakTime:             &req.BreakTime,
		LateEarlyWorker:       &req.LateEarlyWorker,
		ParsedBStartTime:      req.ParsedBStartTime,
		ParsedBEndTime:        req.ParsedBEndTime,
		ParsedBreakTime:       req.ParsedBreakTime,
	}

	// Convert extra time charge
	for _, etc := range req.ExtraTimeCharge {
		calcReq.ExtraTimeCharge = append(calcReq.ExtraTimeCharge, ExtraTimeChargeItem{
			Time:        etc.Time,
			TotalWorker: etc.TotalWorker,
		})
	}

	// Convert statutory
	if req.Statutory != nil {
		calcReq.Statutory = StatutoryCalculationInput{
			Adjustment: req.Statutory.Adjustment,
		}
	}

	// Convert options
	for _, opt := range req.Option {
		calcReq.Option = append(calcReq.Option, OptionCalculationInput(opt))
	}

	// Perform calculation
	calcResp, err := uc.DoSiteReportCalculation(ctx, calcReq)
	if err != nil {
		return log.LogError(err, nil)
	}

	// Create site report with transaction
	return uc.doCreateSiteReport(ctx, DoCreateSiteReportParam{
		Req:      req,
		CalcResp: calcResp,
		UserID:   req.UserID,
	})
}

// updateSiteReport handles updating an existing site report record
func (uc *SiteReportUseCase) updateSiteReport(ctx context.Context, req SaveSiteReportReq) error {
	// Perform site report calculation to get total amount and snapshot data
	calcReq := SiteReportCalculationReq{
		HasStatutory:          req.HasStatutory,
		DepartmentPicID:       req.DepartmentPicID,
		BasicPriceID:          req.BasicPriceID,
		DailyReportAdditions:  req.DailyReportAdditions,
		TotalWorker:           req.TotalWorker,
		DistrictBlockID:       req.DistrictBlockID,
		DistrictBlockUnit:     req.DistrictBlockUnit,
		TransitPlaceBlockID:   req.TransitPlaceBlockID,
		TransitPlaceBlockUnit: req.TransitPlaceBlockUnit,
		BStartTime:            req.BStartTime,
		BEndTime:              req.BEndTime,
		BreakTime:             &req.BreakTime,
		LateEarlyWorker:       &req.LateEarlyWorker,
		ParsedBStartTime:      req.ParsedBStartTime,
		ParsedBEndTime:        req.ParsedBEndTime,
		ParsedBreakTime:       req.ParsedBreakTime,
	}

	// Convert extra time charge
	for _, etc := range req.ExtraTimeCharge {
		calcReq.ExtraTimeCharge = append(calcReq.ExtraTimeCharge, ExtraTimeChargeItem{
			Time:        etc.Time,
			TotalWorker: etc.TotalWorker,
		})
	}

	// Convert statutory
	if req.Statutory != nil {
		calcReq.Statutory = StatutoryCalculationInput{
			Adjustment: req.Statutory.Adjustment,
		}
	}

	// Convert options
	for _, opt := range req.Option {
		calcReq.Option = append(calcReq.Option, OptionCalculationInput(opt))
	}

	// Perform calculation
	calcResp, err := uc.DoSiteReportCalculation(ctx, calcReq)
	if err != nil {
		return log.LogError(err, nil)
	}

	// Update site report with transaction
	return uc.doUpdateSiteReport(ctx, DoUpdateSiteReportParam{
		Req:      req,
		CalcResp: calcResp,
		UserID:   req.UserID,
	})
}

// deleteSiteReport handles deleting a site report record
func (uc *SiteReportUseCase) deleteSiteReport(ctx context.Context, siteReportID int64) error {
	return uc.doDeleteSiteReport(ctx, siteReportID)
}

// doCreateSiteReport handles the database transaction for creating site report
func (uc *SiteReportUseCase) doCreateSiteReport(ctx context.Context, param DoCreateSiteReportParam) error {
	tx := dbLib.InitTx()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. Create site report record
	siteReportID, err := uc.sitereport.CreateSiteReport(ctx, sitereportDmn.CreateSiteReportParam{
		WorkDate:        param.Req.ParsedWorkDate,
		SiteName:        param.Req.SiteName,
		DepartmentPicID: param.Req.DepartmentPicID,
		ReportNumber:    param.Req.ReportNumber,
		HasStatutory:    param.Req.HasStatutory,
		BillDate:        param.Req.ParsedBillDate,
		BasicPriceID:    param.Req.BasicPriceID,

		Worker:              param.Req.TotalWorker,
		DistrictBlockID:     param.Req.DistrictBlockID,
		DistrictBlockUnit:   param.Req.DistrictBlockUnit,
		TransitPlaceBlockID: param.Req.TransitPlaceBlockID,
		TransitPlaceUnit:    param.Req.TransitPlaceBlockUnit,
		BStartTime:          utils.LocalTime{Time: param.Req.ParsedBStartTime},
		BEndTime:            utils.LocalTime{Time: param.Req.ParsedBEndTime},
		SStartTime:          utils.LocalTime{Time: param.Req.ParsedSStartTime},
		SEndTime:            utils.LocalTime{Time: param.Req.ParsedSEndTime},
		BreakTime:           parse.ConvertTimeToLocalTime(param.Req.ParsedBreakTime),
		LateEarlyWorker:     param.Req.LateEarlyWorker,
		ExtraTimeCharge:     uc.convertExtraTimeChargeToJSON(param.Req.ExtraTimeCharge, param.CalcResp.ExtraTimeChargeData),
		Note:                param.Req.Note,
		NoteForInvoice:      param.Req.NoteForInvoice,
		TotalAmount:         param.CalcResp.TotalAmount,
		Snapshot:            uc.generateSiteReportSnapshot(param.CalcResp),
		IsLocked:            false,
		IsInvoiceIssued:     false,
		CreatedBy:           param.UserID,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	})
	if err != nil {
		tx.Rollback()
		return err
	}

	// 2. Create statutory record if has_statutory is true
	if param.Req.HasStatutory && param.Req.Statutory != nil {
		err = uc.sitereportstatutory.CreateWithTx(ctx, tx, sitereportstatutoryDmn.CreateParam{
			SiteReportID:      siteReportID,
			ExpensePerWorker:  param.CalcResp.ExpensePerWorker,
			Adjustment:        param.Req.Statutory.Adjustment,
			TotalAmount:       param.CalcResp.StatutoryAmount,
			Snapshot:          uc.generateStatutorySnapshot(param.CalcResp.StatutoryRate),
			IsStatutoryIssued: false,
			Note:              param.Req.Statutory.Note,
		})
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	// 3. Create option records if provided
	if len(param.Req.Option) > 0 {
		optionItems := make([]sitereportoptionDmn.OptionItem, 0, len(param.Req.Option))
		for _, opt := range param.Req.Option {
			if optionData, exists := param.CalcResp.OptionData[opt.OptionID]; exists {
				optionItems = append(optionItems, sitereportoptionDmn.OptionItem{
					OptionID: opt.OptionID,
					Snapshot: uc.generateOptionSnapshot(optionData),
				})
			}
		}

		if len(optionItems) > 0 {
			err = uc.sitereportoption.BulkCreateWithTx(ctx, tx, sitereportoptionDmn.BulkCreateParam{
				SiteReportID: siteReportID,
				Options:      optionItems,
			})
			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 4. Create daily report addition records if provided
	if len(param.Req.DailyReportAdditions) > 0 {
		dailyReportAdditionItems := make([]sitereportdailyreportadditionDmn.DailyReportAdditionItem, 0, len(param.Req.DailyReportAdditions))
		for _, dra := range param.Req.DailyReportAdditions {
			if draData, exists := param.CalcResp.DailyReportAdditionData[dra.DailyReportAdditionID]; exists {
				dailyReportAdditionItems = append(dailyReportAdditionItems, sitereportdailyreportadditionDmn.DailyReportAdditionItem{
					DailyReportAdditionID: dra.DailyReportAdditionID,
					Snapshot:              uc.generateDailyReportAdditionSnapshot(draData),
				})
			}
		}

		if len(dailyReportAdditionItems) > 0 {
			err = uc.sitereportdailyreportaddition.BulkCreateWithTx(ctx, tx, sitereportdailyreportadditionDmn.BulkCreateParam{
				SiteReportID:         siteReportID,
				DailyReportAdditions: dailyReportAdditionItems,
			})
			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 5. Create worker records
	err = uc.createWorker(ctx, tx, param, siteReportID)
	if err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// doUpdateSiteReport handles the database transaction for updating site report
func (uc *SiteReportUseCase) doUpdateSiteReport(ctx context.Context, param DoUpdateSiteReportParam) error {
	tx := dbLib.InitTx()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. Update site report record
	err := uc.sitereport.UpdateSiteReport(ctx, sitereportDmn.UpdateSiteReportParam{
		ID:              param.Req.ID,
		WorkDate:        param.Req.ParsedWorkDate,
		SiteName:        param.Req.SiteName,
		ReportNumber:    param.Req.ReportNumber,
		DepartmentPicID: param.Req.DepartmentPicID,
		HasStatutory:    param.Req.HasStatutory,
		BillDate:        param.Req.ParsedBillDate,
		BasicPriceID:    param.Req.BasicPriceID,

		Worker:              param.Req.TotalWorker,
		DistrictBlockID:     param.Req.DistrictBlockID,
		DistrictBlockUnit:   param.Req.DistrictBlockUnit,
		TransitPlaceBlockID: param.Req.TransitPlaceBlockID,
		TransitPlaceUnit:    param.Req.TransitPlaceBlockUnit,
		BStartTime:          utils.LocalTime{Time: param.Req.ParsedBStartTime},
		BEndTime:            utils.LocalTime{Time: param.Req.ParsedBEndTime},
		SStartTime:          utils.LocalTime{Time: param.Req.ParsedSStartTime},
		SEndTime:            utils.LocalTime{Time: param.Req.ParsedSEndTime},
		BreakTime:           parse.ConvertTimeToLocalTime(param.Req.ParsedBreakTime),
		LateEarlyWorker:     param.Req.LateEarlyWorker,
		ExtraTimeCharge:     uc.convertExtraTimeChargeToJSON(param.Req.ExtraTimeCharge, param.CalcResp.ExtraTimeChargeData),
		Note:                param.Req.Note,
		NoteForInvoice:      param.Req.NoteForInvoice,
		TotalAmount:         param.CalcResp.TotalAmount,
		Snapshot:            uc.generateSiteReportSnapshot(param.CalcResp),
		UpdatedAt:           time.Now(),
	})
	if err != nil {
		tx.Rollback()
		return log.LogError(err, nil)
	}

	// 2. Handle statutory record
	err = uc.updateSiteReportStatutory(ctx, tx, param)
	if err != nil {
		tx.Rollback()
		return log.LogError(err, nil)
	}

	// 3. Handle option records with optimized update-or-create-or-delete logic
	err = uc.manageSiteReportOptions(ctx, tx, ManageSiteReportOptionsParam{
		SiteReportID:      param.Req.ID,
		RequestedOptions:  param.Req.Option,
		CalculatedOptions: param.CalcResp.OptionData,
	})
	if err != nil {
		tx.Rollback()
		return log.LogError(err, nil)
	}

	// 4. Handle daily report addition records with optimized update-or-create-or-delete logic
	// Convert from GetDetailListDailyReportAdditionResp to SaveSiteReportDailyReportAddition
	requestedDailyReportAdditions := make([]SaveSiteReportDailyReportAddition, 0, len(param.Req.DailyReportAdditions))
	for _, dra := range param.Req.DailyReportAdditions {
		requestedDailyReportAdditions = append(requestedDailyReportAdditions, SaveSiteReportDailyReportAddition{
			DailyReportAdditionID: dra.DailyReportAdditionID,
		})
	}

	err = uc.manageSiteReportDailyReportAdditions(ctx, tx, ManageSiteReportDailyReportAdditionsParam{
		SiteReportID:                   param.Req.ID,
		RequestedDailyReportAdditions:  requestedDailyReportAdditions,
		CalculatedDailyReportAdditions: param.CalcResp.DailyReportAdditionData,
	})
	if err != nil {
		tx.Rollback()
		return log.LogError(err, nil)
	}

	// 5. Update worker records using individual worker updates
	err = uc.updateSiteReportWorker(ctx, tx, param)
	if err != nil {
		tx.Rollback()
		return log.LogError(err, nil)
	}

	return tx.Commit().Error
}

// doDeleteSiteReport handles the database transaction for deleting site report
func (uc *SiteReportUseCase) doDeleteSiteReport(ctx context.Context, siteReportID int64) error {
	// Soft delete site_report record (soft delete by setting deleted_at)
	err := uc.sitereport.DeleteSiteReport(ctx, sitereportDmn.DeleteSiteReportParam{
		ID:        siteReportID,
		DeletedAt: time.Now(),
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// convertExtraTimeChargeToJSON converts extra time charge items to JSON string
func (uc *SiteReportUseCase) convertExtraTimeChargeToJSON(items []SaveSiteReportExtraTimeItem, itemData map[string]ExtraTimeChargeItemData) string {
	if len(items) == 0 {
		return "[]"
	}

	jsonItems := make([]sitereportDmn.ExtraTimeChargeItem, len(items))
	for i, item := range items {
		jsonItems[i] = sitereportDmn.ExtraTimeChargeItem{
			Time:        item.Time,
			Price:       itemData[item.Time].Price,
			TotalWorker: item.TotalWorker,
		}
	}

	jsonBytes, err := json.Marshal(jsonItems)
	if err != nil {
		return "[]"
	}

	return string(jsonBytes)
}

// generateSiteReportSnapshot generates snapshot JSON for site report
func (uc *SiteReportUseCase) generateSiteReportSnapshot(calcResp DoSiteReportCalculationResp) string {
	snapshot := sitereportDmn.Snapshot{
		CustomerID:               calcResp.CustomerID,
		CustomerName:             calcResp.CustomerName,
		DepartmentID:             calcResp.DepartmentID,
		DepartmentName:           calcResp.DepartmentName,
		DepartmentPicName:        calcResp.DepartmentPicName,
		BasicPriceName:           calcResp.BasicPriceName,
		ExpensePerWorker:         calcResp.ExpensePerWorker,
		TotalDailyReportAddition: calcResp.TotalDailyReportAddition,
		DistrictBlockName:        calcResp.DistrictBlockName,
		DistrictBlockAmount:      calcResp.DistrictBlockAmount,
		TransitPlaceBlockName:    calcResp.TransitPlaceBlockName,
		TransitPlaceBlockAmount:  calcResp.TransitPlaceBlockAmount,
	}

	jsonBytes, err := json.Marshal(snapshot)
	if err != nil {
		return "{}"
	}

	return string(jsonBytes)
}

// generateStatutorySnapshot generates snapshot JSON for statutory
func (uc *SiteReportUseCase) generateStatutorySnapshot(rate float64) string {
	snapshot := sitereportstatutoryDmn.Snapshot{
		Rate: rate,
	}

	jsonBytes, err := json.Marshal(snapshot)
	if err != nil {
		return "{}"
	}

	return string(jsonBytes)
}

// generateOptionSnapshot generates snapshot JSON for option
func (uc *SiteReportUseCase) generateOptionSnapshot(option OptionData) string {
	snapshot := sitereportoptionDmn.Snapshot{
		Title:  option.Title,
		Amount: option.Amount,
	}

	jsonBytes, err := json.Marshal(snapshot)
	if err != nil {
		return "{}"
	}

	return string(jsonBytes)
}

// createWorker creates worker records for a site report
func (uc *SiteReportUseCase) createWorker(ctx context.Context, tx *gorm.DB, param DoCreateSiteReportParam, siteReportID int64) error {
	for _, worker := range param.Req.Worker {
		// Get user information for snapshot
		user, err := uc.user.GetByID(ctx, worker.WorkerID)
		if err != nil {
			return log.LogError(err, nil)
		}
		if user.ID == 0 {
			return ErrUserNotFound
		}

		qualificationAllowanceIDs := make([]int64, len(worker.Qualifications))
		for i, qual := range worker.Qualifications {
			qualificationAllowanceIDs[i] = qual.QualificationID
		}

		calcResp, err := uc.DoWorkerCalculation(ctx, WorkerCalculationParam{
			WorkerID:                  worker.WorkerID,
			BasicPriceID:              param.Req.BasicPriceID,
			StartTime:                 worker.StartTime,
			EndTime:                   worker.EndTime,
			BreakTime:                 worker.BreakTime,
			TransportExpense:          worker.TransportExpense,
			LeaderAllowance:           worker.LeaderAllowance,
			DistantFeeID:              parse.Int64Pointer(worker.DistantFeeID),
			QualificationAllowanceIDs: qualificationAllowanceIDs,
			ParsedStartTime:           worker.ParsedStartTime,
			ParsedEndTime:             worker.ParsedEndTime,
			ParsedBreakTime:           worker.ParsedBreakTime,
		})
		if err != nil {
			return log.LogError(err, nil)
		}

		// Crete worker snapshot
		workerSnapshotJSON, err := json.Marshal(sitereportworkerDmn.Snapshot{
			WorkerName: user.Name,
			DistantFee: calcResp.DistantFee,
		})
		if err != nil {
			return log.LogError(err, nil)
		}

		// Create worker record
		workerID, err := uc.sitereportworker.CreateWorkerWithTx(ctx, tx, sitereportworkerDmn.CreateWorkerParam{
			SiteReportID:          siteReportID,
			UserID:                worker.WorkerID,
			StartTime:             worker.ParsedStartTime,
			EndTime:               worker.ParsedEndTime,
			BreakTime:             worker.ParsedBreakTime,
			TransportationExpense: worker.TransportExpense,
			LeaderAllowance:       worker.LeaderAllowance,
			DistantFeeID:          worker.DistantFeeID,
			IncomeTaxID:           calcResp.TaxIncomeID,
			Tax:                   calcResp.TaxAmount,
			Amount:                calcResp.TotalAmountAfterTax,
			Snapshot:              string(workerSnapshotJSON),
			Status:                sitereportworkerDmn.StatusUnpaid,
			IssuedDate:            nil,
		})
		if err != nil {
			return log.LogError(err, nil)
		}

		calculatedQualificationMap := make(map[int64]Qualification)
		for _, qual := range calcResp.Qualifications {
			calculatedQualificationMap[qual.ID] = qual
		}

		// Create worker qualification records
		if len(worker.Qualifications) > 0 {
			qualifications := make([]sitereportworkerqualificationDmn.QualificationData, 0, len(worker.Qualifications))
			for _, qual := range worker.Qualifications {
				snapshotJSON, err := json.Marshal(sitereportworkerqualificationDmn.Snapshot{
					Title:    calculatedQualificationMap[qual.QualificationID].Title,
					AddClaim: calculatedQualificationMap[qual.QualificationID].AddClaim,
				})
				if err != nil {
					return log.LogError(err, nil)
				}

				qualifications = append(qualifications, sitereportworkerqualificationDmn.QualificationData{
					QualificationID: qual.QualificationID,
					Snapshot:        string(snapshotJSON),
				})
			}

			err = uc.sitereportworkerqualification.BulkInsertWithTx(ctx, tx, sitereportworkerqualificationDmn.BulkInsertParam{
				SiteReportWorkerID: workerID,
				Qualifications:     qualifications,
			})
			if err != nil {
				return log.LogError(err, nil)
			}
		}
	}

	return nil
}

func (uc *SiteReportUseCase) updateSiteReportStatutory(ctx context.Context, tx *gorm.DB, param DoUpdateSiteReportParam) error {
	statutoryExists, err := uc.sitereportstatutory.ExistsBySiteReportIDWithTx(ctx, tx, sitereportstatutoryDmn.ExistsBySiteReportIDParam{
		SiteReportID: param.Req.ID,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	if param.Req.HasStatutory && param.Req.Statutory != nil {
		// When has_statutory is true: Update existing record or create new one
		if statutoryExists {
			// Update existing record
			err = uc.sitereportstatutory.UpdateWithTx(ctx, tx, sitereportstatutoryDmn.UpdateParam{
				SiteReportID:      param.Req.ID,
				ExpensePerWorker:  param.CalcResp.ExpensePerWorker,
				Adjustment:        param.Req.Statutory.Adjustment,
				TotalAmount:       param.CalcResp.StatutoryAmount,
				Snapshot:          uc.generateStatutorySnapshot(param.CalcResp.StatutoryRate),
				IsStatutoryIssued: false,
				Note:              param.Req.Statutory.Note,
			})
			if err != nil {
				return log.LogError(err, nil)
			}
		} else {
			// Create new record
			err = uc.sitereportstatutory.CreateWithTx(ctx, tx, sitereportstatutoryDmn.CreateParam{
				SiteReportID:      param.Req.ID,
				ExpensePerWorker:  param.CalcResp.ExpensePerWorker,
				Adjustment:        param.Req.Statutory.Adjustment,
				TotalAmount:       param.CalcResp.StatutoryAmount,
				Snapshot:          uc.generateStatutorySnapshot(param.CalcResp.StatutoryRate),
				IsStatutoryIssued: false,
				Note:              param.Req.Statutory.Note,
			})
			if err != nil {
				return log.LogError(err, nil)
			}
		}
	} else {
		// When has_statutory is false: Delete existing record if it exists
		if statutoryExists {
			err = uc.sitereportstatutory.DeleteBySiteReportIDWithTx(ctx, tx, sitereportstatutoryDmn.DeleteBySiteReportIDParam{
				SiteReportID: param.Req.ID,
			})
			if err != nil {
				return log.LogError(err, nil)
			}
		}
		// If no record exists, skip (no action needed)
	}

	return nil
}

// manageSiteReportOptions handles the complex option management logic
func (uc *SiteReportUseCase) manageSiteReportOptions(ctx context.Context, tx *gorm.DB, param ManageSiteReportOptionsParam) error {
	// Get existing options for this site report
	existingOptions, err := uc.sitereportoption.GetBySiteReportIDWithTx(ctx, tx, sitereportoptionDmn.GetBySiteReportIDParam{
		SiteReportID: param.SiteReportID,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	// Create maps for easier comparison
	existingOptionMap := make(map[int64]bool)
	for _, option := range existingOptions {
		existingOptionMap[option.OptionID] = true
	}

	// Create maps for easier comparison
	requestedOptionMap := make(map[int64]bool)
	for _, option := range param.RequestedOptions {
		requestedOptionMap[option.OptionID] = true
	}

	// Process option changes
	err = uc.processOptionChanges(ctx, tx, ProcessOptionChangesParam{
		SiteReportID:       param.SiteReportID,
		ExistingOptionMap:  existingOptionMap,
		RequestedOptionMap: requestedOptionMap,
		OptionDataMap:      param.CalculatedOptions,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// processOptionChanges handles the bulk operations for option changes
func (uc *SiteReportUseCase) processOptionChanges(ctx context.Context, tx *gorm.DB, param ProcessOptionChangesParam) error {
	var toUpdate []sitereportoptionDmn.OptionUpdateData
	var toInsert []sitereportoptionDmn.OptionItem
	var toDelete []int64

	// Determine operations needed
	for optionID := range param.RequestedOptionMap {
		if param.ExistingOptionMap[optionID] {
			// Update existing option
			if optionData, exists := param.OptionDataMap[optionID]; exists {
				toUpdate = append(toUpdate, sitereportoptionDmn.OptionUpdateData{
					OptionID: optionID,
					Snapshot: uc.generateOptionSnapshot(optionData),
				})
			}
		} else {
			// Insert new option
			if optionData, exists := param.OptionDataMap[optionID]; exists {
				toInsert = append(toInsert, sitereportoptionDmn.OptionItem{
					OptionID: optionID,
					Snapshot: uc.generateOptionSnapshot(optionData),
				})
			}
		}
	}

	// Find options to delete
	for optionID := range param.ExistingOptionMap {
		if !param.RequestedOptionMap[optionID] {
			toDelete = append(toDelete, optionID)
		}
	}

	// Execute bulk operations
	err := uc.executeBulkOptionOperations(ctx, tx, ExecuteBulkOptionOperationsParam{
		SiteReportID: param.SiteReportID,
		ToUpdate:     toUpdate,
		ToInsert:     toInsert,
		ToDelete:     toDelete,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// executeBulkOptionOperations performs the actual bulk database operations asynchronously
func (uc *SiteReportUseCase) executeBulkOptionOperations(ctx context.Context, tx *gorm.DB, param ExecuteBulkOptionOperationsParam) error {
	// Execute bulk update
	if len(param.ToUpdate) > 0 {
		err := uc.sitereportoption.BulkUpdateWithTx(ctx, tx, sitereportoptionDmn.BulkUpdateParam{
			SiteReportID: param.SiteReportID,
			Options:      param.ToUpdate,
		})
		if err != nil {
			return log.LogError(err, nil)
		}
	}

	// Execute bulk insert
	if len(param.ToInsert) > 0 {
		err := uc.sitereportoption.BulkCreateWithTx(ctx, tx, sitereportoptionDmn.BulkCreateParam{
			SiteReportID: param.SiteReportID,
			Options:      param.ToInsert,
		})
		if err != nil {
			return log.LogError(err, nil)
		}
	}

	// Execute bulk delete asynchronously
	if len(param.ToDelete) > 0 {
		err := uc.sitereportoption.BulkDeleteWithTx(ctx, tx, sitereportoptionDmn.BulkDeleteParam{
			SiteReportID: param.SiteReportID,
			OptionIDs:    param.ToDelete,
		})
		if err != nil {
			return log.LogError(err, nil)
		}
	}

	return nil
}

// manageSiteReportDailyReportAdditions handles the complex daily report addition management logic
func (uc *SiteReportUseCase) manageSiteReportDailyReportAdditions(ctx context.Context, tx *gorm.DB, param ManageSiteReportDailyReportAdditionsParam) error {
	// Get existing daily report additions for this site report
	existingDailyReportAdditions, err := uc.sitereportdailyreportaddition.GetBySiteReportIDWithTx(ctx, tx, sitereportdailyreportadditionDmn.GetBySiteReportIDParam{
		SiteReportID: param.SiteReportID,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	// Create maps for easier comparison
	existingDailyReportAdditionMap := make(map[int64]bool)
	for _, dra := range existingDailyReportAdditions {
		existingDailyReportAdditionMap[dra.DailyReportAdditionID] = true
	}

	// Create maps for easier comparison
	requestedDailyReportAdditionMap := make(map[int64]bool)
	for _, dra := range param.RequestedDailyReportAdditions {
		requestedDailyReportAdditionMap[dra.DailyReportAdditionID] = true
	}

	// Process daily report addition changes
	err = uc.processDailyReportAdditionChanges(ctx, tx, ProcessDailyReportAdditionChangesParam{
		SiteReportID:                    param.SiteReportID,
		ExistingDailyReportAdditionMap:  existingDailyReportAdditionMap,
		RequestedDailyReportAdditionMap: requestedDailyReportAdditionMap,
		DailyReportAdditionDataMap:      param.CalculatedDailyReportAdditions,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// processDailyReportAdditionChanges handles the bulk operations for daily report addition changes
func (uc *SiteReportUseCase) processDailyReportAdditionChanges(ctx context.Context, tx *gorm.DB, param ProcessDailyReportAdditionChangesParam) error {
	var toUpdate []sitereportdailyreportadditionDmn.DailyReportAdditionUpdateData
	var toInsert []sitereportdailyreportadditionDmn.DailyReportAdditionItem
	var toDelete []int64

	// Determine operations needed
	for dailyReportAdditionID := range param.RequestedDailyReportAdditionMap {
		if param.ExistingDailyReportAdditionMap[dailyReportAdditionID] {
			// Update existing daily report addition
			if draData, exists := param.DailyReportAdditionDataMap[dailyReportAdditionID]; exists {
				toUpdate = append(toUpdate, sitereportdailyreportadditionDmn.DailyReportAdditionUpdateData{
					DailyReportAdditionID: dailyReportAdditionID,
					Snapshot:              uc.generateDailyReportAdditionSnapshot(draData),
				})
			}
		} else {
			// Insert new daily report addition
			if draData, exists := param.DailyReportAdditionDataMap[dailyReportAdditionID]; exists {
				toInsert = append(toInsert, sitereportdailyreportadditionDmn.DailyReportAdditionItem{
					DailyReportAdditionID: dailyReportAdditionID,
					Snapshot:              uc.generateDailyReportAdditionSnapshot(draData),
				})
			}
		}
	}

	// Find daily report additions to delete
	for dailyReportAdditionID := range param.ExistingDailyReportAdditionMap {
		if !param.RequestedDailyReportAdditionMap[dailyReportAdditionID] {
			toDelete = append(toDelete, dailyReportAdditionID)
		}
	}

	// Execute bulk update
	if len(toUpdate) > 0 {
		err := uc.sitereportdailyreportaddition.BulkUpdateWithTx(ctx, tx, sitereportdailyreportadditionDmn.BulkUpdateParam{
			SiteReportID:         param.SiteReportID,
			DailyReportAdditions: toUpdate,
		})
		if err != nil {
			return log.LogError(err, nil)
		}
	}

	// Execute bulk insert
	if len(toInsert) > 0 {
		err := uc.sitereportdailyreportaddition.BulkCreateWithTx(ctx, tx, sitereportdailyreportadditionDmn.BulkCreateParam{
			SiteReportID:         param.SiteReportID,
			DailyReportAdditions: toInsert,
		})
		if err != nil {
			return log.LogError(err, nil)
		}
	}

	// Execute bulk delete
	if len(toDelete) > 0 {
		err := uc.sitereportdailyreportaddition.BulkDeleteBySiteReportIDAndDailyReportAdditionIDsWithTx(ctx, tx, param.SiteReportID, toDelete)
		if err != nil {
			return log.LogError(err, nil)
		}
	}

	return nil
}

// generateDailyReportAdditionSnapshot generates snapshot JSON for daily report addition
func (uc *SiteReportUseCase) generateDailyReportAdditionSnapshot(data DailyReportAdditionData) string {
	snapshot := sitereportdailyreportadditionDmn.Snapshot{
		Title:           data.Title,
		AmountPerSite:   data.AmountPerSite,
		AmountPerWorker: data.AmountPerWorker,
	}

	jsonBytes, err := json.Marshal(snapshot)
	if err != nil {
		return "{}"
	}

	return string(jsonBytes)
}

func (uc *SiteReportUseCase) updateSiteReportWorker(ctx context.Context, tx *gorm.DB, param DoUpdateSiteReportParam) error {
	existingWorkers, err := uc.sitereportworker.GetBySiteReportID(ctx, param.Req.ID)
	if err != nil {
		return log.LogError(err, nil)
	}

	existingWorkerMap := make(map[int64]bool)
	for _, worker := range existingWorkers {
		existingWorkerMap[worker.UserID] = true
	}

	existingWorkerDataMap := make(map[int64]sitereportworkerDmn.SiteReportWorker)
	for _, worker := range existingWorkers {
		existingWorkerDataMap[worker.UserID] = worker
	}

	requestedWorkerMap := make(map[int64]bool)
	for _, worker := range param.Req.Worker {
		requestedWorkerMap[worker.WorkerID] = true
	}

	requestedWorkerDataMap := make(map[int64]SaveSiteReportWorker)
	for _, worker := range param.Req.Worker {
		requestedWorkerDataMap[worker.WorkerID] = worker
	}

	for workerID := range requestedWorkerMap {
		if existingWorkerMap[workerID] {
			// Update existing worker
			if workerData, exists := requestedWorkerDataMap[workerID]; exists {
				var distantFeeID int64
				if workerData.DistantFeeID != nil {
					distantFeeID = *workerData.DistantFeeID
				}

				workerReq := SaveWorkerReq{
					ID:               workerData.ID,
					WorkerID:         workerData.WorkerID,
					BasicPriceID:     param.Req.BasicPriceID,
					StartTime:        workerData.StartTime,
					EndTime:          workerData.EndTime,
					BreakTime:        workerData.BreakTime,
					TransportExpense: workerData.TransportExpense,
					LeaderAllowance:  workerData.LeaderAllowance,
					DistantFeeID:     distantFeeID,
					Qualifications:   workerData.Qualifications,
					ParsedStartTime:  workerData.ParsedStartTime,
					ParsedEndTime:    workerData.ParsedEndTime,
					ParsedBreakTime:  workerData.ParsedBreakTime,
				}

				err := uc.updateWorker(ctx, tx, workerReq)
				if err != nil {
					return log.LogError(err, nil)
				}
			}
		} else {
			// Insert new worker
			if workerData, exists := requestedWorkerDataMap[workerID]; exists {
				singleParam := DoCreateSiteReportParam(param)
				singleParam.Req.Worker = []SaveSiteReportWorker{workerData}
				err := uc.createWorker(ctx, tx, singleParam, param.Req.ID)
				if err != nil {
					return log.LogError(err, nil)
				}
			}
		}
	}

	// Delete workers that are no longer requested
	for workerID := range existingWorkerMap {
		if !requestedWorkerMap[workerID] {
			err := uc.deleteWorker(ctx, tx, existingWorkerDataMap[workerID].ID)
			if err != nil {
				return log.LogError(err, nil)
			}
		}
	}

	return nil
}

// GetDetailList retrieves detailed site report information with related data
func (uc *SiteReportUseCase) GetDetailList(ctx context.Context, req GetDetailListReq) ([]GetDetailListResp, error) {
	// Convert request to domain parameter
	param := sitereportDmn.GetDetailListParam{
		IsPreload: true,
	}

	if req.WorkDate != "" {
		param.WorkDate = &req.ParsedWorkDate
	}

	if req.ID > 0 {
		param.ID = &req.ID
	}

	// Get site reports from domain
	reports, err := uc.sitereport.GetDetailList(ctx, param)
	if err != nil {
		return []GetDetailListResp{}, log.LogError(err, nil)
	}
	if len(reports) == 0 {
		return []GetDetailListResp{}, nil
	}

	// Convert to response format with detailed information
	return buildDetailListResponse(reports)
}

// buildDetailListResponse builds the detailed response with all related data
func buildDetailListResponse(reports []sitereportDmn.SiteReport) ([]GetDetailListResp, error) {
	result := make([]GetDetailListResp, 0, len(reports))

	for _, report := range reports {
		resp, err := buildSingleDetailResponse(report)
		if err != nil {
			return []GetDetailListResp{}, log.LogError(err, nil)
		}
		result = append(result, resp)
	}

	return result, nil
}

// buildSingleDetailResponse builds a single detailed response with all related data
func buildSingleDetailResponse(report sitereportDmn.SiteReport) (GetDetailListResp, error) {
	resp := GetDetailListResp{
		ID:              report.ID,
		WorkDate:        report.WorkDate.Format(time.DateOnly),
		IsLocked:        report.IsLocked,
		SiteName:        report.SiteName,
		ReportNumber:    report.ReportNumber,
		HasStatutory:    report.HasStatutory,
		DepartmentPicID: report.DepartmentPicID,
		BillDate:        report.BillDate.Format(time.DateOnly),
		BasicPriceID:    report.BasicPriceID,

		TotalWorker:           report.Worker,
		DistrictBlockID:       report.DistrictBlockID,
		DistrictBlockUnit:     report.DistrictBlockUnit,
		TransitPlaceBlockID:   parse.Int64Pointer(report.TransitPlaceBlockID),
		TransitPlaceBlockUnit: parse.Int64Pointer(report.TransitPlaceUnit),
		BStartTime:            report.BStartTime.Format(time.TimeOnly),
		BEndTime:              report.BEndTime.Format(time.TimeOnly),
		SStartTime:            report.SStartTime.Format(time.TimeOnly),
		SEndTime:              report.SEndTime.Format(time.TimeOnly),
		BreakTime:             report.BreakTime.Format(time.TimeOnly),
		LateEarlyWorker:       report.LateEarlyWorker,
		Note:                  report.Note,
		NoteForInvoice:        report.NoteForInvoice,
		TotalAmount:           report.TotalAmount,
	}

	// Parse extra_time_charge JSON
	var extraTimeCharge []ExtraTimeChargeItem
	err := json.Unmarshal([]byte(report.ExtraTimeCharge), &extraTimeCharge)
	if err != nil {
		return GetDetailListResp{}, log.LogError(err, nil)
	}
	resp.ExtraTimeCharge = extraTimeCharge

	// Handle related data based on is_locked status
	if report.IsLocked {
		// Use snapshot data
		resp, err = populateFromSnapshot(resp, report)
		if err != nil {
			return GetDetailListResp{}, log.LogError(err, nil)
		}
	} else {
		// Fetch from related tables
		resp, err = populateFromRelatedTables(resp, report)
		if err != nil {
			return GetDetailListResp{}, log.LogError(err, nil)
		}
	}

	// Fetch nested objects (statutory, option, worker)
	resp, err = populateNestedObjects(resp, report)
	if err != nil {
		return GetDetailListResp{}, log.LogError(err, nil)
	}

	return resp, nil
}

// populateFromSnapshot populates response data from snapshot JSON for locked reports
func populateFromSnapshot(resp GetDetailListResp, report sitereportDmn.SiteReport) (GetDetailListResp, error) {
	// Parse snapshot JSON
	var snapshot sitereportDmn.Snapshot
	err := json.Unmarshal([]byte(report.Snapshot), &snapshot)
	if err != nil {
		return GetDetailListResp{}, log.LogError(err, nil)
	}

	// Extract customer info
	resp.CustomerID = snapshot.CustomerID
	resp.CustomerName = snapshot.CustomerName

	// Extract department info
	resp.DepartmentID = snapshot.DepartmentID
	resp.DepartmentName = snapshot.DepartmentName

	// Extract department pic name
	resp.DepartmentPicName = snapshot.DepartmentPicName

	// Extract basic price name
	resp.BasicPriceName = snapshot.BasicPriceName

	// Extract block names
	resp.DistrictBlockName = snapshot.DistrictBlockName
	resp.TransitPlaceBlockName = snapshot.TransitPlaceBlockName

	return resp, nil
}

// populateFromRelatedTables populates response data from preloaded relationships for unlocked reports
func populateFromRelatedTables(resp GetDetailListResp, report sitereportDmn.SiteReport) (GetDetailListResp, error) {
	// Department PIC and related data (Department → Customer chain)
	if report.DepartmentPic.ID != 0 {
		resp.DepartmentPicName = report.DepartmentPic.PicName
		resp.DepartmentID = report.DepartmentPic.DepartmentID

		if report.DepartmentPic.Department.ID != 0 {
			resp.DepartmentName = report.DepartmentPic.Department.Name
			resp.CustomerID = report.DepartmentPic.Department.CustomerID

			if report.DepartmentPic.Department.Customer.ID != 0 {
				resp.CustomerName = report.DepartmentPic.Department.Customer.Name
			}
		}
	}

	// Basic price data
	if report.BasicPrice.ID != 0 {
		resp.BasicPriceName = report.BasicPrice.Title
	}

	// Block data
	if report.DistrictBlock.ID != 0 {
		resp.DistrictBlockName = report.DistrictBlock.Name
	}
	if report.TransitPlaceBlock.ID != 0 {
		resp.TransitPlaceBlockName = report.TransitPlaceBlock.Name
	}

	return resp, nil
}

// populateNestedObjects processes preloaded nested objects (statutory, option, worker) data
func populateNestedObjects(resp GetDetailListResp, report sitereportDmn.SiteReport) (GetDetailListResp, error) {
	// Process statutory data from preloaded relationships
	resp, err := processStatutoryData(resp, report)
	if err != nil {
		return resp, log.LogError(err, nil)
	}

	// Process option data from preloaded relationships
	resp, err = processOptionData(resp, report)
	if err != nil {
		return resp, log.LogError(err, nil)
	}

	// Process daily report addition data from preloaded relationships
	resp, err = processDailyReportAdditionData(resp, report)
	if err != nil {
		return resp, log.LogError(err, nil)
	}

	// Process worker data from preloaded relationships
	resp, err = processWorkerData(resp, report)
	if err != nil {
		return resp, log.LogError(err, nil)
	}

	return resp, nil
}

// processStatutoryData processes preloaded statutory information for the site report
func processStatutoryData(resp GetDetailListResp, report sitereportDmn.SiteReport) (GetDetailListResp, error) {
	if !report.HasStatutory {
		resp.Statutory = nil
		return resp, nil
	}

	// Check if we have preloaded statutory data
	if len(report.Statutory) == 0 {
		resp.Statutory = nil
		return resp, nil
	}

	// Use the first statutory record (should only be one per site report)
	statutory := report.Statutory[0]
	var rate float64

	if report.IsLocked {
		// Parse snapshot for rate
		var snapshot sitereportstatutoryDmn.Snapshot
		err := json.Unmarshal([]byte(statutory.Snapshot), &snapshot)
		if err != nil {
			return resp, log.LogError(err, nil)
		}
		rate = snapshot.Rate
	} else {
		// For unlocked reports, get the rate from the preloaded customer's statutory
		if report.DepartmentPic.Department.Customer.Statutory.ID != 0 {
			rate = report.DepartmentPic.Department.Customer.Statutory.Rate
		}
	}

	resp.Statutory = &GetDetailListStatutoryResp{
		ID:               statutory.ID,
		Rate:             rate,
		ExpensePerWorker: statutory.ExpensePerWorker,
		Adjustment:       statutory.Adjustment,
		TotalAmount:      statutory.TotalAmount,
		Note:             statutory.Note,
	}

	return resp, nil
}

// processOptionData processes preloaded option information for the site report
func processOptionData(resp GetDetailListResp, report sitereportDmn.SiteReport) (GetDetailListResp, error) {
	// Process preloaded options
	resp.Option = make([]GetDetailListOptionResp, 0, len(report.Options))

	for _, option := range report.Options {
		if report.IsLocked {
			// Parse snapshot for name and amount
			var snapshot sitereportoptionDmn.Snapshot
			var name string
			var amount float64

			err := json.Unmarshal([]byte(option.Snapshot), &snapshot)
			if err != nil {
				return resp, log.LogError(err, nil)
			}
			name = snapshot.Title
			amount = snapshot.Amount

			resp.Option = append(resp.Option, GetDetailListOptionResp{
				ID:       option.ID,
				OptionID: option.OptionID,
				Name:     name,
				Amount:   amount,
			})
		} else {
			// For unlocked reports, get the option details from preloaded option
			if option.Option.ID != 0 {
				resp.Option = append(resp.Option, GetDetailListOptionResp{
					ID:       option.ID,
					OptionID: option.OptionID,
					Name:     option.Option.Title,
					Amount:   option.Option.AddClaim,
				})
			}
		}
	}

	return resp, nil
}

// processDailyReportAdditionData processes preloaded daily report addition information for the site report
func processDailyReportAdditionData(resp GetDetailListResp, report sitereportDmn.SiteReport) (GetDetailListResp, error) {
	// Process preloaded daily report additions
	resp.DailyReportAdditions = make([]GetDetailListDailyReportAdditionResp, 0, len(report.DailyReportAdditions))

	for _, dailyReportAddition := range report.DailyReportAdditions {
		if report.IsLocked {
			// Parse snapshot for title and amounts
			var snapshot sitereportdailyreportadditionDmn.Snapshot
			var title string
			var amountPerSite, amountPerWorker float64

			err := json.Unmarshal([]byte(dailyReportAddition.Snapshot), &snapshot)
			if err != nil {
				return resp, log.LogError(err, nil)
			}
			title = snapshot.Title
			amountPerSite = snapshot.AmountPerSite
			amountPerWorker = snapshot.AmountPerWorker

			resp.DailyReportAdditions = append(resp.DailyReportAdditions, GetDetailListDailyReportAdditionResp{
				ID:                    dailyReportAddition.ID,
				DailyReportAdditionID: dailyReportAddition.DailyReportAdditionID,
				Title:                 title,
				AmountPerSite:         amountPerSite,
				AmountPerWorker:       amountPerWorker,
			})
		} else {
			// For unlocked reports, get the daily report addition details from preloaded daily report addition
			if dailyReportAddition.DailyReportAddition.ID != 0 {
				resp.DailyReportAdditions = append(resp.DailyReportAdditions, GetDetailListDailyReportAdditionResp{
					ID:                    dailyReportAddition.ID,
					DailyReportAdditionID: dailyReportAddition.DailyReportAdditionID,
					Title:                 dailyReportAddition.DailyReportAddition.Title,
					AmountPerSite:         dailyReportAddition.DailyReportAddition.AmountPerSite,
					AmountPerWorker:       dailyReportAddition.DailyReportAddition.AmountPerHour,
				})
			}
		}
	}

	return resp, nil
}

// processWorkerData processes preloaded worker information for the site report
func processWorkerData(resp GetDetailListResp, report sitereportDmn.SiteReport) (GetDetailListResp, error) {
	// Process preloaded workers
	resp.Worker = make([]GetDetailListWorkerResp, 0, len(report.Workers))

	for _, worker := range report.Workers {
		if report.IsLocked {
			// Parse snapshot for worker name
			var snapshot sitereportworkerDmn.Snapshot

			err := json.Unmarshal([]byte(worker.Snapshot), &snapshot)
			if err != nil {
				return resp, log.LogError(err, nil)
			}

			qualifications := make([]Qualification, len(worker.Qualifications))
			for i, qualification := range worker.Qualifications {
				var qualSnapshot sitereportworkerqualificationDmn.Snapshot
				err := json.Unmarshal([]byte(qualification.Snapshot), &qualSnapshot)
				if err != nil {
					return resp, log.LogError(err, nil)
				}

				qualifications[i] = Qualification{
					ID:              qualification.ID,
					QualificationID: qualification.QualificationID,
					Title:           qualSnapshot.Title,
					AddClaim:        qualSnapshot.AddClaim,
				}
			}

			resp.Worker = append(resp.Worker, GetDetailListWorkerResp{
				ID:               worker.ID,
				WorkerID:         worker.UserID,
				Name:             snapshot.WorkerName,
				StartTime:        worker.StartTime.Format(time.TimeOnly),
				EndTime:          worker.EndTime.Format(time.TimeOnly),
				BreakTime:        worker.BreakTime.Format(time.TimeOnly),
				TransportExpense: worker.TransportationExpense,
				LeaderAllowance:  worker.LeaderAllowance,
				DistantFeeID:     parse.Int64Pointer(worker.DistantFeeID),
				DistantFeeName:   snapshot.DistantFeeName,
				Qualifications:   qualifications,
			})
		} else {
			// For unlocked reports, get the user name from preloaded user
			if worker.User.ID != 0 {
				qualifications := make([]Qualification, len(worker.Qualifications))
				for i, qualification := range worker.Qualifications {
					qualifications[i] = Qualification{
						ID:              qualification.ID,
						QualificationID: qualification.QualificationID,
						Title:           qualification.Qualification.Title,
						AddClaim:        qualification.Qualification.AddClaim,
					}
				}

				var breakTime string
				if worker.BreakTime != nil {
					breakTime = worker.BreakTime.Format(time.TimeOnly)
				}

				resp.Worker = append(resp.Worker, GetDetailListWorkerResp{
					ID:               worker.ID,
					WorkerID:         worker.UserID,
					Name:             worker.User.Name,
					StartTime:        worker.StartTime.Format(time.TimeOnly),
					EndTime:          worker.EndTime.Format(time.TimeOnly),
					BreakTime:        breakTime,
					TransportExpense: worker.TransportationExpense,
					LeaderAllowance:  worker.LeaderAllowance,
					DistantFeeID:     parse.Int64Pointer(worker.DistantFeeID),
					DistantFeeName:   worker.DistantFee.Title,
					Qualifications:   qualifications,
				})
			}
		}
	}

	return resp, nil
}

// doUpdateWorker handles the database transaction for updating worker and qualifications
func (uc *SiteReportUseCase) doUpdateWorker(ctx context.Context, tx *gorm.DB, param DoUpdateWorkerParam) error {
	// Update the worker record
	err := uc.sitereportworker.UpdateWorkerWithTx(ctx, tx, sitereportworkerDmn.UpdateWorkerParam{
		ID:                    param.Req.ID,
		UserID:                param.Req.WorkerID,
		StartTime:             param.Req.ParsedStartTime,
		EndTime:               param.Req.ParsedEndTime,
		BreakTime:             param.Req.ParsedBreakTime,
		TransportationExpense: param.Req.TransportExpense,
		LeaderAllowance:       param.Req.LeaderAllowance,
		DistantFeeID:          parse.ZeroInt64Pointer(param.Req.DistantFeeID),
		IncomeTaxID:           param.CalcResp.TaxIncomeID,
		Tax:                   param.CalcResp.TaxAmount,
		Amount:                param.CalcResp.TotalAmount,
		Snapshot:              param.WorkerSnapshotJSON,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	// Handle qualification management
	qualificationIDs := make([]int64, len(param.Req.Qualifications))
	for i, q := range param.Req.Qualifications {
		qualificationIDs[i] = q.QualificationID
	}

	err = uc.manageWorkerQualifications(ctx, tx, ManageWorkerQualificationsParam{
		WorkerID:                  param.Req.ID,
		RequestedQualificationIDs: qualificationIDs,
		CalculatedQualifications:  param.CalcResp.Qualifications,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// manageWorkerQualifications handles the complex qualification management logic
func (uc *SiteReportUseCase) manageWorkerQualifications(ctx context.Context, tx *gorm.DB, param ManageWorkerQualificationsParam) error {
	// Get existing qualifications for this worker
	existingQualifications, err := uc.sitereportworkerqualification.GetByWorkerID(ctx, sitereportworkerqualificationDmn.GetByWorkerIDParam{
		SiteReportWorkerID: param.WorkerID,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	existingQualMap := make(map[int64]bool)
	for _, qual := range existingQualifications {
		existingQualMap[qual.QualificationID] = true
	}

	requestedQualMap := make(map[int64]bool)
	for _, qualID := range param.RequestedQualificationIDs {
		requestedQualMap[qualID] = true
	}

	// Create qualification data map from calculated qualifications
	qualDataMap := make(map[int64]Qualification)
	for _, qual := range param.CalculatedQualifications {
		qualDataMap[qual.ID] = qual
	}

	// Process qualification changes
	err = uc.processQualificationChanges(ctx, tx, ProcessQualificationChangesParam{
		WorkerID:         param.WorkerID,
		ExistingQualMap:  existingQualMap,
		RequestedQualMap: requestedQualMap,
		QualDataMap:      qualDataMap,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// processQualificationChanges handles the bulk operations for qualification changes
func (uc *SiteReportUseCase) processQualificationChanges(ctx context.Context, tx *gorm.DB, param ProcessQualificationChangesParam) error {
	var toUpdate []sitereportworkerqualificationDmn.QualificationUpdateData
	var toInsert []sitereportworkerqualificationDmn.QualificationData
	var toDelete []int64

	// Determine operations needed
	for qualID := range param.RequestedQualMap {
		if param.ExistingQualMap[qualID] {
			// Update existing qualification
			if qualData, exists := param.QualDataMap[qualID]; exists {
				snapshot := sitereportworkerqualificationDmn.Snapshot{
					Title:    qualData.Title,
					AddClaim: qualData.AddClaim,
				}
				snapshotJSON, err := json.Marshal(snapshot)
				if err != nil {
					return log.LogError(err, nil)
				}
				toUpdate = append(toUpdate, sitereportworkerqualificationDmn.QualificationUpdateData{
					QualificationID: qualID,
					Snapshot:        string(snapshotJSON),
				})
			}
		} else {
			// Insert new qualification
			if qualData, exists := param.QualDataMap[qualID]; exists {
				snapshot := sitereportworkerqualificationDmn.Snapshot{
					Title:    qualData.Title,
					AddClaim: qualData.AddClaim,
				}
				snapshotJSON, err := json.Marshal(snapshot)
				if err != nil {
					return log.LogError(err, nil)
				}
				toInsert = append(toInsert, sitereportworkerqualificationDmn.QualificationData{
					QualificationID: qualID,
					Snapshot:        string(snapshotJSON),
				})
			}
		}
	}

	// Find qualifications to delete
	for qualID := range param.ExistingQualMap {
		if !param.RequestedQualMap[qualID] {
			toDelete = append(toDelete, qualID)
		}
	}

	// Execute bulk operations
	err := uc.executeBulkQualificationOperations(ctx, tx, ExecuteBulkQualificationOperationsParam{
		WorkerID: param.WorkerID,
		ToUpdate: toUpdate,
		ToInsert: toInsert,
		ToDelete: toDelete,
	})
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// executeBulkQualificationOperations performs the actual bulk database operations asynchronously
func (uc *SiteReportUseCase) executeBulkQualificationOperations(ctx context.Context, tx *gorm.DB, param ExecuteBulkQualificationOperationsParam) error {
	if len(param.ToUpdate) > 0 {
		if err := uc.sitereportworkerqualification.BulkUpdateWithTx(ctx, tx, sitereportworkerqualificationDmn.BulkUpdateParam{
			SiteReportWorkerID: param.WorkerID,
			Qualifications:     param.ToUpdate,
		}); err != nil {
			return log.LogError(err, nil)
		}
	}

	if len(param.ToInsert) > 0 {
		if err := uc.sitereportworkerqualification.BulkInsertWithTx(ctx, tx, sitereportworkerqualificationDmn.BulkInsertParam{
			SiteReportWorkerID: param.WorkerID,
			Qualifications:     param.ToInsert,
		}); err != nil {
			return log.LogError(err, nil)
		}
	}

	if len(param.ToDelete) > 0 {
		if err := uc.sitereportworkerqualification.BulkDeleteWithTx(ctx, tx, sitereportworkerqualificationDmn.BulkDeleteParam{
			SiteReportWorkerID: param.WorkerID,
			QualificationIDs:   param.ToDelete,
		}); err != nil {
			return log.LogError(err, nil)
		}
	}

	return nil
}

// calculateWorkingHours calculates working hours excluding break time
func (uc *SiteReportUseCase) calculateWorkingHours(startTime, endTime time.Time, breakTime *time.Time) float64 {
	// Calculate total duration
	totalDuration := endTime.Sub(startTime)

	// Subtract 1 hour from end time as per requirement
	adjustedEndTime := endTime.Add(-1 * time.Hour)
	if adjustedEndTime.Before(startTime) {
		return 0
	}

	// Recalculate duration with adjusted end time
	totalDuration = adjustedEndTime.Sub(startTime)

	// Subtract break time if it falls within working hours
	if breakTime != nil {
		if breakTime.After(startTime) && breakTime.Before(adjustedEndTime) {
			// Break time is within working hours, subtract 1 hour
			totalDuration -= time.Hour
		}
	}

	// Convert to hours
	hours := totalDuration.Hours()
	if hours < 0 {
		return 0
	}

	return hours
}

// calculateStatutoryRate calculates the statutory rate for a customer
func (uc *SiteReportUseCase) calculateStatutoryRate(ctx context.Context, customerID int64) (float64, error) {
	statutory, err := uc.statutory.GetByCustomerID(ctx, statutoryDmn.GetByCustomerIDParam{
		CustomerID: customerID,
	})
	if err != nil {
		return 0, err
	}
	if statutory.ID == 0 {
		return 0, errors.New("法定が見つかりません")
	}

	return statutory.Rate, nil
}

// calculateExpensePerWorker calculates expense per worker from basic price JSON
func (uc *SiteReportUseCase) calculateExpensePerWorker(ctx context.Context, param CalculateExpensePerWorker) (CalculateExpensePerWorkerResp, error) {
	basicPrice, err := uc.basicPrice.GetByID(ctx, basicpriceDmn.GetByIDParam{
		ID: param.BasicPriceID,
	})
	if err != nil {
		return CalculateExpensePerWorkerResp{}, err
	}
	if basicPrice.ID == 0 {
		return CalculateExpensePerWorkerResp{}, errors.New("契約タイプが見つかりません")
	}

	// Parse the price JSON
	var priceData []PriceItem
	err = json.Unmarshal([]byte(basicPrice.PriceJson), &priceData)
	if err != nil {
		return CalculateExpensePerWorkerResp{}, fmt.Errorf("failed to parse price_json: %w", err)
	}

	// Calculate total expense
	var totalExpense float64

	// Generate hours from start to end (excluding end hour)
	currentTime := param.StartTime
	for currentTime.Before(param.EndTime) {
		hourStr := currentTime.Format("15:04")

		// Skip break time if provided
		if param.BreakTime != nil && currentTime.Hour() == param.BreakTime.Hour() && currentTime.Minute() == param.BreakTime.Minute() {
			currentTime = currentTime.Add(time.Hour)
			continue
		}

		// Find price for this hour
		for _, pi := range priceData {
			if pi.Hour == hourStr {
				totalExpense += pi.Price
				break
			}
		}

		currentTime = currentTime.Add(time.Hour)
	}

	resp := CalculateExpensePerWorkerResp{
		TotalExpense:   totalExpense,
		BasicPriceName: basicPrice.Title,
	}

	return resp, nil
}

// calculateAddFees calculates additional fees from daily report addition
func (uc *SiteReportUseCase) calculateAddFees(ctx context.Context, dailyReportAdditionIDs string) (CalculateAddFeesResp, error) {
	// If no daily report addition ID provided, return 0 for both
	if dailyReportAdditionIDs == "" {
		return CalculateAddFeesResp{}, nil
	}

	// Parse daily report addition IDs
	idList := utils.SplitStringToInt64(dailyReportAdditionIDs)
	if len(idList) == 0 {
		return CalculateAddFeesResp{}, nil
	}

	// Get daily report addition data
	additions, err := uc.dailyReportAddition.GetByIDs(ctx, dailyreportadditionDmn.GetByIDsParam{
		IDs: idList,
	})
	if err != nil {
		return CalculateAddFeesResp{}, err
	}
	if len(additions) == 0 {
		return CalculateAddFeesResp{}, errors.New("日報追加が見つかりません")
	}

	addFeePerSite := 0.0
	addFeePerWorker := 0.0

	for _, addition := range additions {
		addFeePerSite += addition.AmountPerSite
		addFeePerWorker += addition.AmountPerHour
	}

	return CalculateAddFeesResp{
		AddFeePerSite:   addFeePerSite,
		AddFeePerWorker: addFeePerWorker,
	}, nil
}

// calculateMultipleAddFees calculates additional fees from multiple daily report additions
func (uc *SiteReportUseCase) calculateMultipleAddFees(ctx context.Context, dailyReportAdditionIDs []int64) (map[int64]DailyReportAdditionData, float64, float64, error) {
	dailyReportAdditionData := make(map[int64]DailyReportAdditionData)
	var totalAddFeePerSite, totalAddFeePerWorker float64

	if len(dailyReportAdditionIDs) == 0 {
		return dailyReportAdditionData, 0, 0, nil
	}

	additions, err := uc.dailyReportAddition.GetByIDs(ctx, dailyreportadditionDmn.GetByIDsParam{
		IDs: dailyReportAdditionIDs,
	})
	if err != nil {
		return nil, 0, 0, err
	}

	for _, addition := range additions {
		if addition.ID == 0 {
			return nil, 0, 0, fmt.Errorf("日報追加が見つかりません (ID: %d)", addition.ID)
		}

		dailyReportAdditionData[addition.ID] = DailyReportAdditionData{
			Title:           addition.Title,
			AmountPerSite:   addition.AmountPerSite,
			AmountPerWorker: addition.AmountPerHour,
		}

		totalAddFeePerSite += addition.AmountPerSite
		totalAddFeePerWorker += addition.AmountPerHour
	}

	return dailyReportAdditionData, totalAddFeePerSite, totalAddFeePerWorker, nil
}

// SiteReportCalculation performs site report calculation based on the provided parameters
func (uc *SiteReportUseCase) SiteReportCalculation(ctx context.Context, req SiteReportCalculationReq) (SiteReportCalculationResp, error) {
	resp, err := uc.DoSiteReportCalculation(ctx, req)
	if err != nil {
		return SiteReportCalculationResp{}, log.LogError(err, nil)
	}

	return SiteReportCalculationResp{
		TotalAmount:     resp.TotalAmount,
		StatutoryAmount: resp.StatutoryAmount,
	}, nil
}

func (uc *SiteReportUseCase) DoSiteReportCalculation(ctx context.Context, req SiteReportCalculationReq) (DoSiteReportCalculationResp, error) {
	var (
		resp                        DoSiteReportCalculationResp
		timeRange                   float64
		expensePerWorkerResp        CalculateExpensePerWorkerResp
		dailyReportAdditionData     map[int64]DailyReportAdditionData
		totalAddFeePerSite          float64
		totalAddFeePerWorker        float64
		districtBlockAmountResp     CalculateDistrictBlockAmountResp
		transitPlaceBlockAmountResp CalculateTransitPlaceBlockAmountResp
		lateEarlyAmount             float64
		extraTimeChargeResp         CalculateExtraTimeChargeAmountResp
		optionAmountResp            CalculateOptionAmountResp
		statutoryRateResp           CalculateStatutoryRateFromDepartmentPicResp
	)

	// Use errgroup to perform calculations concurrently
	g, gctx := errgroup.WithContext(ctx)

	// Calculate time range
	timeRange = uc.calculateTimeRange(CalculateTimeRangeParam{
		StartTime: req.ParsedBStartTime,
		EndTime:   req.ParsedBEndTime,
		BreakTime: req.ParsedBreakTime,
	})

	// Calculate expense per worker
	g.Go(func() error {
		var err error
		expensePerWorkerResp, err = uc.calculateExpensePerWorker(gctx, CalculateExpensePerWorker{
			BasicPriceID: req.BasicPriceID,
			StartTime:    req.ParsedBStartTime,
			EndTime:      req.ParsedBEndTime,
			BreakTime:    req.ParsedBreakTime,
		})
		return err
	})

	// Calculate add fees
	g.Go(func() error {
		var err error
		// Extract IDs from the DailyReportAdditions array
		dailyReportAdditionIDs := make([]int64, 0, len(req.DailyReportAdditions))
		for _, dra := range req.DailyReportAdditions {
			dailyReportAdditionIDs = append(dailyReportAdditionIDs, dra.DailyReportAdditionID)
		}
		dailyReportAdditionData, totalAddFeePerSite, totalAddFeePerWorker, err = uc.calculateMultipleAddFees(gctx, dailyReportAdditionIDs)
		return err
	})

	// Calculate district block amount
	g.Go(func() error {
		var err error
		districtBlockAmountResp, err = uc.calculateDistrictBlockAmount(gctx, CalculateDistrictBlockAmountParam{
			BlockID:   req.DistrictBlockID,
			BlockUnit: req.DistrictBlockUnit,
			TimeRange: timeRange,
		})
		return err
	})

	// Calculate transit place block amount
	g.Go(func() error {
		var err error
		transitPlaceBlockAmountResp, err = uc.calculateTransitPlaceBlockAmount(gctx, CalculateTransitPlaceBlockAmountParam{
			BlockID:   req.TransitPlaceBlockID,
			BlockUnit: req.TransitPlaceBlockUnit,
			TimeRange: timeRange,
		})
		return err
	})

	// Calculate late early amount
	lateEarlyAmount = calculateLateEarlyAmount(parse.Int64Pointer(req.LateEarlyWorker))

	// Calculate extra time charge amount
	g.Go(func() error {
		var err error
		extraTimeChargeResp, err = uc.calculateExtraTimeChargeAmount(gctx, CalculateExtraTimeChargeAmountParam{
			ExtraTimeCharges: req.ExtraTimeCharge,
			BasicPriceID:     req.BasicPriceID,
		})
		return err
	})

	// Calculate option amount
	g.Go(func() error {
		var err error
		optionAmountResp, err = uc.calculateOptionAmount(gctx, req.Option)
		return err
	})

	// Calculate statutory rate if needed
	if req.HasStatutory {
		g.Go(func() error {
			var err error
			statutoryRateResp, err = uc.calculateStatutoryRateFromDepartmentPic(gctx, req.DepartmentPicID)
			return err
		})
	}

	// Wait for all calculations to complete
	if err := g.Wait(); err != nil {
		return DoSiteReportCalculationResp{}, log.LogError(err, nil)
	}

	// Calculate add fee amount
	addFeeAmount := totalAddFeePerSite + (timeRange * totalAddFeePerWorker)

	// Calculate total amount
	totalAmount := addFeeAmount + districtBlockAmountResp.Amount + transitPlaceBlockAmountResp.Amount + (expensePerWorkerResp.TotalExpense * float64(req.TotalWorker)) + lateEarlyAmount + extraTimeChargeResp.TotalAmount + optionAmountResp.TotalAmount

	// Calculate statutory amount if needed
	var statutoryAmount float64
	if req.HasStatutory {
		statutoryAmount = (float64(req.TotalWorker)*expensePerWorkerResp.TotalExpense*totalAddFeePerWorker + totalAddFeePerSite + req.Statutory.Adjustment) * statutoryRateResp.Rate
	}

	resp.TotalAmount = totalAmount
	resp.StatutoryAmount = statutoryAmount
	resp.CustomerID = statutoryRateResp.CustomerID
	resp.CustomerName = statutoryRateResp.CustomerName
	resp.DepartmentID = statutoryRateResp.DepartmentID
	resp.DepartmentName = statutoryRateResp.DepartmentName
	resp.DepartmentPicName = statutoryRateResp.DepartmentPicName
	resp.BasicPriceName = expensePerWorkerResp.BasicPriceName
	resp.TotalDailyReportAddition = addFeeAmount
	resp.DailyReportAdditionData = dailyReportAdditionData
	resp.DistrictBlockName = districtBlockAmountResp.BlockName
	resp.DistrictBlockAmount = districtBlockAmountResp.Amount
	resp.TransitPlaceBlockName = transitPlaceBlockAmountResp.BlockName
	resp.TransitPlaceBlockAmount = transitPlaceBlockAmountResp.Amount
	resp.ExpensePerWorker = expensePerWorkerResp.TotalExpense
	resp.StatutoryRate = statutoryRateResp.Rate
	resp.OptionData = optionAmountResp.OptionData
	resp.ExtraTimeChargeData = extraTimeChargeResp.Data

	return resp, nil
}

// calculateTimeRange calculates total hours between start and end time, subtracting break time if provided
func (uc *SiteReportUseCase) calculateTimeRange(param CalculateTimeRangeParam) float64 {
	// Calculate total duration
	totalDuration := param.EndTime.Sub(param.StartTime)

	// Subtract break time if provided
	if param.BreakTime != nil {
		totalDuration -= time.Hour
	}

	// Convert to hours
	hours := totalDuration.Hours()
	if hours < 0 {
		return 0
	}

	return hours
}

// calculateDistrictBlockAmount calculates district block amount
func (uc *SiteReportUseCase) calculateDistrictBlockAmount(ctx context.Context, param CalculateDistrictBlockAmountParam) (CalculateDistrictBlockAmountResp, error) {
	block, err := uc.block.GetByID(ctx, blockDmn.GetByIDParam{
		ID: param.BlockID,
	})
	if err != nil {
		return CalculateDistrictBlockAmountResp{}, err
	}
	if block.ID == 0 {
		return CalculateDistrictBlockAmountResp{}, errors.New("地区ブロックが見つかりません")
	}

	// district_block_amount = (district_block_unit_price × district_block_unit) + (district_block_price_per_worker × time_range)
	amount := (block.UnitPrice * float64(param.BlockUnit)) + (block.PricePerWorker * param.TimeRange)
	return CalculateDistrictBlockAmountResp{
		Amount:    amount,
		BlockName: block.Name,
	}, nil
}

// calculateTransitPlaceBlockAmount calculates transit place block amount
func (uc *SiteReportUseCase) calculateTransitPlaceBlockAmount(ctx context.Context, param CalculateTransitPlaceBlockAmountParam) (CalculateTransitPlaceBlockAmountResp, error) {
	if param.BlockID == nil || param.BlockUnit == nil {
		return CalculateTransitPlaceBlockAmountResp{}, nil
	}

	block, err := uc.block.GetByID(ctx, blockDmn.GetByIDParam{
		ID: *param.BlockID,
	})
	if err != nil {
		return CalculateTransitPlaceBlockAmountResp{}, err
	}
	if block.ID == 0 {
		return CalculateTransitPlaceBlockAmountResp{}, errors.New("交通場所ブロックが見つかりません")
	}

	// transit_place_block_amount = ((transit_place_block_unit_price ÷ 2) × transit_place_block_unit) + ((transit_place_block_additional_price_per_worker ÷ 2) × time_range)
	amount := ((block.UnitPrice / 2) * float64(*param.BlockUnit)) + ((block.PricePerWorker / 2) * param.TimeRange)
	return CalculateTransitPlaceBlockAmountResp{
		Amount:    amount,
		BlockName: block.Name,
	}, nil
}

// calculateLateEarlyAmount calculates late early amount
func calculateLateEarlyAmount(lateEarlyWorker int64) float64 {
	// late_early_amount = 1500 × late_early_worker
	return LateEarlyBaseAmount * float64(lateEarlyWorker)
}

// calculateExtraTimeChargeAmount calculates extra time charge amount
func (uc *SiteReportUseCase) calculateExtraTimeChargeAmount(ctx context.Context, param CalculateExtraTimeChargeAmountParam) (CalculateExtraTimeChargeAmountResp, error) {
	if len(param.ExtraTimeCharges) == 0 {
		return CalculateExtraTimeChargeAmountResp{}, nil
	}

	// Get basic price to lookup time values
	basicPrice, err := uc.basicPrice.GetByID(ctx, basicpriceDmn.GetByIDParam{
		ID: param.BasicPriceID,
	})
	if err != nil {
		return CalculateExtraTimeChargeAmountResp{}, err
	}
	if basicPrice.ID == 0 {
		return CalculateExtraTimeChargeAmountResp{}, errors.New("契約タイプが見つかりません")
	}

	// Parse the price JSON
	var priceData []PriceItem
	if err := json.Unmarshal([]byte(basicPrice.PriceJson), &priceData); err != nil {
		return CalculateExtraTimeChargeAmountResp{}, fmt.Errorf("failed to parse price_json: %w", err)
	}

	var totalAmount float64
	data := make(map[string]ExtraTimeChargeItemData)
	for _, charge := range param.ExtraTimeCharges {
		// Find price for this time
		for _, pi := range priceData {
			if pi.Hour+":00" == charge.Time {
				totalAmount += pi.Price * float64(charge.TotalWorker)

				data[charge.Time] = ExtraTimeChargeItemData{
					Price: pi.Price,
				}

				break
			}
		}
	}

	resp := CalculateExtraTimeChargeAmountResp{
		TotalAmount: totalAmount,
		Data:        data,
	}

	return resp, nil
}

// calculateOptionAmount calculates option amount
func (uc *SiteReportUseCase) calculateOptionAmount(ctx context.Context, options []OptionCalculationInput) (CalculateOptionAmountResp, error) {
	if len(options) == 0 {
		return CalculateOptionAmountResp{}, nil
	}

	// Extract option IDs
	var optionIDs []int64
	for _, opt := range options {
		optionIDs = append(optionIDs, opt.OptionID)
	}

	// Get options from domain
	optionRecords, err := uc.option.GetByIDs(ctx, optionDmn.GetByIDsParam{
		IDs: optionIDs,
	})
	if err != nil {
		return CalculateOptionAmountResp{}, err
	}

	// Sum all add_claim values
	var totalAmount float64
	var optionData = make(map[int64]OptionData)
	for _, option := range optionRecords {
		totalAmount += option.AddClaim
		optionData[option.ID] = OptionData{
			Title:  option.Title,
			Amount: option.AddClaim,
		}
	}

	return CalculateOptionAmountResp{
		TotalAmount: totalAmount,
		OptionData:  optionData,
	}, nil
}

// calculateStatutoryRateFromDepartmentPic calculates statutory rate from department pic ID
func (uc *SiteReportUseCase) calculateStatutoryRateFromDepartmentPic(ctx context.Context, departmentPicID int64) (CalculateStatutoryRateFromDepartmentPicResp, error) {
	// Get department pic with customer info
	departmentPic, err := uc.departmentPic.GetByIDWithCustomer(ctx, departmentpicDmn.GetByIDWithCustomerParam{
		ID: departmentPicID,
	})
	if err != nil {
		return CalculateStatutoryRateFromDepartmentPicResp{}, err
	}
	if departmentPic.ID == 0 {
		return CalculateStatutoryRateFromDepartmentPicResp{}, errors.New("部門の写真が見つかりません")
	}

	// Get statutory rate by customer ID
	statutory, err := uc.statutory.GetByCustomerID(ctx, statutoryDmn.GetByCustomerIDParam{
		CustomerID: departmentPic.CustomerID,
	})
	if err != nil {
		return CalculateStatutoryRateFromDepartmentPicResp{}, err
	}
	if statutory.ID == 0 {
		return CalculateStatutoryRateFromDepartmentPicResp{}, errors.New("法定率が見つかりません")
	}

	return CalculateStatutoryRateFromDepartmentPicResp{
		Rate:              statutory.Rate,
		CustomerID:        departmentPic.CustomerID,
		CustomerName:      departmentPic.CustomerName,
		DepartmentID:      departmentPic.DepartmentID,
		DepartmentName:    departmentPic.DepartmentName,
		DepartmentPicName: departmentPic.PicName,
	}, nil
}

// GetDeliverySlip retrieves delivery slip data for a site report
func (uc *SiteReportUseCase) GetDeliverySlip(ctx context.Context, req GetDeliverySlipReq) (GetDeliverySlipResp, error) {
	// Get site report with all necessary relationships
	param := sitereportDmn.GetDetailListParam{
		ID:        &req.SiteReportID,
		IsPreload: true,
	}

	reports, err := uc.sitereport.GetDetailList(ctx, param)
	if err != nil {
		return GetDeliverySlipResp{}, log.LogError(err, nil)
	}
	if len(reports) == 0 {
		return GetDeliverySlipResp{}, errors.New("サイトレポートが見つかりません")
	}

	report := reports[0]

	// calculate site report
	var calcResp DoSiteReportCalculationResp
	if !report.IsLocked {
		calcResp, err = uc.calculateSiteReportFromExisting(ctx, report)
		if err != nil {
			return GetDeliverySlipResp{}, log.LogError(err, nil)
		}
	}

	// Build delivery slip response
	resp, err := buildDeliverySlipResponse(report, calcResp)
	if err != nil {
		return GetDeliverySlipResp{}, log.LogError(err, nil)
	}

	return resp, nil
}

func (uc *SiteReportUseCase) calculateSiteReportFromExisting(ctx context.Context, report sitereportDmn.SiteReport) (DoSiteReportCalculationResp, error) {
	breakTime := report.BreakTime.Format(time.TimeOnly)

	calcReq := SiteReportCalculationReq{
		HasStatutory:          report.HasStatutory,
		DepartmentPicID:       report.DepartmentPicID,
		BasicPriceID:          report.BasicPriceID,
		TotalWorker:           report.Worker,
		DistrictBlockID:       report.DistrictBlockID,
		DistrictBlockUnit:     report.DistrictBlockUnit,
		TransitPlaceBlockID:   report.TransitPlaceBlockID,
		TransitPlaceBlockUnit: report.TransitPlaceUnit,
		BStartTime:            report.BStartTime.Format(time.TimeOnly),
		BEndTime:              report.BEndTime.Format(time.TimeOnly),
		BreakTime:             &breakTime,
		LateEarlyWorker:       &report.LateEarlyWorker,
		ParsedBStartTime:      report.BStartTime.Time,
		ParsedBEndTime:        report.BEndTime.Time,
		ParsedBreakTime:       &report.BreakTime.Time,
	}

	// convert daily report additions
	for _, dra := range report.DailyReportAdditions {
		calcReq.DailyReportAdditions = append(calcReq.DailyReportAdditions, GetDetailListDailyReportAdditionResp{
			ID:                    dra.ID,
			DailyReportAdditionID: dra.DailyReportAdditionID,
		})
	}

	// Convert extra time charge
	extraTimeCharge := []SaveSiteReportExtraTimeItem{}
	err := json.Unmarshal([]byte(report.ExtraTimeCharge), &extraTimeCharge)
	if err != nil {
		return DoSiteReportCalculationResp{}, err
	}

	for _, etc := range extraTimeCharge {
		calcReq.ExtraTimeCharge = append(calcReq.ExtraTimeCharge, ExtraTimeChargeItem{
			Time:        etc.Time,
			TotalWorker: etc.TotalWorker,
		})
	}

	// Convert statutory
	if len(report.Statutory) > 0 {
		calcReq.Statutory = StatutoryCalculationInput{
			Adjustment: report.Statutory[0].Adjustment,
		}
	}

	// Convert options
	for _, opt := range report.Options {
		calcReq.Option = append(calcReq.Option, OptionCalculationInput{
			OptionID: opt.OptionID,
		})
	}

	return uc.DoSiteReportCalculation(ctx, calcReq)
}

// buildDeliverySlipResponse builds the delivery slip response from site report data
func buildDeliverySlipResponse(report sitereportDmn.SiteReport, calcResp DoSiteReportCalculationResp) (GetDeliverySlipResp, error) {
	var err error
	resp := GetDeliverySlipResp{
		IssueDate:      utils.FormatJapaneseDate(time.Now()),
		SiteName:       report.SiteName,
		WorkDate:       utils.FormatJapaneseDate(report.WorkDate),
		Expiration:     DefaultDeliverySlipExpiration,
		DefaultAddress: DefaultDeliverySlipAddress,
		TotalAmount:    report.TotalAmount,
		NoteForInvoice: report.NoteForInvoice,
	}

	if report.IsLocked {
		resp, err = buildLockedDeliverySlip(BuildDeliverySlipParam{
			Report: report,
			Resp:   resp,
		})
		if err != nil {
			return GetDeliverySlipResp{}, log.LogError(err, nil)
		}
	} else {
		resp, err = buildUnlockedDeliverySlip(BuildDeliverySlipParam{
			Report:   report,
			CalcResp: calcResp,
			Resp:     resp,
		})
		if err != nil {
			return GetDeliverySlipResp{}, log.LogError(err, nil)
		}
	}

	return resp, nil
}

func buildLockedDeliverySlip(param BuildDeliverySlipParam) (GetDeliverySlipResp, error) {
	// Get customer name and department pic name based on is_locked status
	err := json.Unmarshal([]byte(param.Report.Snapshot), &param.ReportSnapshot)
	if err != nil {
		return GetDeliverySlipResp{}, log.LogError(err, nil)
	}

	param.Resp.CustomerName = param.ReportSnapshot.CustomerName
	param.Resp.DepartmentPicName = param.ReportSnapshot.DepartmentPicName

	// Get statutory data
	if len(param.Report.Statutory) > 0 {
		statutory := param.Report.Statutory[0]

		var statutorySnapshot sitereportstatutoryDmn.Snapshot
		err = json.Unmarshal([]byte(statutory.Snapshot), &statutorySnapshot)
		if err != nil {
			return GetDeliverySlipResp{}, log.LogError(err, nil)
		}

		param.Resp.Statutory = GetDeliverySlipStatutory{
			Rate:        statutorySnapshot.Rate,
			TotalAmount: statutory.TotalAmount,
		}
	}

	// Get main table data
	mainTable, err := buildLockedDeliverySlipMainTable(param)
	if err != nil {
		return GetDeliverySlipResp{}, log.LogError(err, nil)
	}
	param.Resp.MainTable = mainTable

	return param.Resp, nil
}

func buildUnlockedDeliverySlip(param BuildDeliverySlipParam) (GetDeliverySlipResp, error) {
	// Get customer name and department pic name based on is_locked status
	if param.Report.DepartmentPic.Department.Customer.ID != 0 {
		param.Resp.CustomerName = param.Report.DepartmentPic.Department.Customer.Name
	}

	if param.Report.DepartmentPic.ID != 0 {
		param.Resp.DepartmentPicName = param.Report.DepartmentPic.PicName
	}

	// Get statutory data
	if len(param.Report.Statutory) > 0 {
		statutory := param.Report.Statutory[0]
		var rate float64
		if param.Report.DepartmentPic.Department.Customer.Statutory.ID != 0 {
			rate = param.Report.DepartmentPic.Department.Customer.Statutory.Rate
		}

		param.Resp.Statutory = GetDeliverySlipStatutory{
			Rate:        rate,
			TotalAmount: statutory.TotalAmount,
		}
	}

	// Get main table data
	mainTable, err := buildUnlockedDeliverySlipMainTable(param)
	if err != nil {
		return GetDeliverySlipResp{}, log.LogError(err, nil)
	}
	param.Resp.MainTable = mainTable

	return param.Resp, nil
}

func buildLockedDeliverySlipMainTable(param BuildDeliverySlipParam) ([]GetDeliverySlipMainTable, error) {
	resp := []GetDeliverySlipMainTable{}
	var err error

	// basic price
	resp = buildLockedDeliverySlipBasicPrice(param, resp)

	// add fee
	resp = buildLockedDeliverySlipAddFee(param, resp)

	// district block
	resp = buildLockedDeliverySlipDistrictBlock(param, resp)

	// transit place block
	resp = buildLockedDeliverySlipTransitPlaceBlock(param, resp)

	// late early
	resp = buildDeliverySlipLateEarly(param, resp)

	// extra time charge
	resp, err = buildLockedDeliverySlipExtraTimeCharge(param, resp)
	if err != nil {
		return resp, log.LogError(err, nil)
	}

	// option
	resp = buildLockedDeliverySlipOption(param, resp)

	return resp, nil
}

func buildUnlockedDeliverySlipMainTable(param BuildDeliverySlipParam) ([]GetDeliverySlipMainTable, error) {
	resp := []GetDeliverySlipMainTable{}
	var err error

	// basic price
	resp = buildUnlockedDeliverySlipBasicPrice(param, resp)

	// add fee
	resp = buildUnlockedDeliverySlipAddFee(param, resp)

	// district block
	resp = buildUnlockedDeliverySlipDistrictBlock(param, resp)

	// transit place block
	resp = buildUnlockedDeliverySlipTransitPlaceBlock(param, resp)

	// late early
	resp = buildDeliverySlipLateEarly(param, resp)

	// extra time charge
	resp, err = buildUnlockedDeliverySlipExtraTimeCharge(param, resp)
	if err != nil {
		return resp, log.LogError(err, nil)
	}

	// option
	resp = buildUnlockedDeliverySlipOption(param, resp)

	return resp, nil
}

func buildLockedDeliverySlipBasicPrice(param BuildDeliverySlipParam, resp []GetDeliverySlipMainTable) []GetDeliverySlipMainTable {
	bStartTime := param.Report.BStartTime.Format(time.TimeOnly)[:5] // HH:MM
	bEndTime := param.Report.BEndTime.Format(time.TimeOnly)[:5]     // HH:MM
	quantity := param.Report.Worker
	price := param.ReportSnapshot.ExpensePerWorker
	totalPrice := price * float64(quantity)

	resp = append(resp, GetDeliverySlipMainTable{
		Item:       fmt.Sprintf("%s %s~%s", param.ReportSnapshot.BasicPriceName, bStartTime, bEndTime),
		Quantity:   quantity,
		Unit:       UnitPiece,
		Price:      price,
		TotalPrice: totalPrice,
	})

	return resp
}

func buildLockedDeliverySlipAddFee(param BuildDeliverySlipParam, resp []GetDeliverySlipMainTable) []GetDeliverySlipMainTable {
	if len(param.Report.DailyReportAdditions) == 0 {
		return resp
	}

	item := AddFeeLabel
	price := param.ReportSnapshot.TotalDailyReportAddition

	for i, dra := range param.Report.DailyReportAdditions {
		snapshot := sitereportdailyreportadditionDmn.Snapshot{}
		err := json.Unmarshal([]byte(dra.Snapshot), &snapshot)
		if err != nil {
			return resp
		}

		item += snapshot.Title
		if i < len(param.Report.DailyReportAdditions)-1 {
			item += ", "
		}
	}

	resp = append(resp, GetDeliverySlipMainTable{
		Item:       item,
		Quantity:   1,
		Unit:       UnitPiece,
		Price:      price,
		TotalPrice: price,
	})

	return resp
}

func buildLockedDeliverySlipDistrictBlock(param BuildDeliverySlipParam, resp []GetDeliverySlipMainTable) []GetDeliverySlipMainTable {
	// block name
	resp = append(resp, GetDeliverySlipMainTable{
		Item:     fmt.Sprintf("%s %s", DistrictBlockNameLabel, param.ReportSnapshot.DistrictBlockName),
		Quantity: 1,
		Unit:     UnitPiece,
	})

	// block unit
	price := param.ReportSnapshot.DistrictBlockAmount
	resp = append(resp, GetDeliverySlipMainTable{
		Item:       DistrictBlockUnitLabel,
		Quantity:   param.Report.DistrictBlockUnit,
		Unit:       UnitTravel,
		Price:      price,
		TotalPrice: price,
	})

	return resp
}

func buildLockedDeliverySlipTransitPlaceBlock(param BuildDeliverySlipParam, resp []GetDeliverySlipMainTable) []GetDeliverySlipMainTable {
	if param.Report.TransitPlaceBlockID == nil {
		return resp
	}

	// block name
	resp = append(resp, GetDeliverySlipMainTable{
		Item:     fmt.Sprintf("%s %s", TransitPlaceBlockNameLabel, param.ReportSnapshot.TransitPlaceBlockName),
		Quantity: 1,
		Unit:     UnitPiece,
	})

	// block unit
	price := param.ReportSnapshot.TransitPlaceBlockAmount
	resp = append(resp, GetDeliverySlipMainTable{
		Item:       TransitPlaceBlockUnitLabel,
		Quantity:   parse.Int64Pointer(param.Report.TransitPlaceUnit),
		Unit:       UnitTravel,
		Price:      price,
		TotalPrice: price,
	})

	return resp
}

func buildDeliverySlipLateEarly(param BuildDeliverySlipParam, resp []GetDeliverySlipMainTable) []GetDeliverySlipMainTable {
	quantity := param.Report.LateEarlyWorker
	if quantity == 0 {
		return resp
	}

	resp = append(resp, GetDeliverySlipMainTable{
		Item:       LateEarlyLabel,
		Quantity:   quantity,
		Unit:       UnitPiece,
		Price:      LateEarlyBaseAmount,
		TotalPrice: calculateLateEarlyAmount(quantity),
	})

	return resp
}

func buildLockedDeliverySlipExtraTimeCharge(param BuildDeliverySlipParam, resp []GetDeliverySlipMainTable) ([]GetDeliverySlipMainTable, error) {
	if param.Report.ExtraTimeCharge == "[]" {
		return resp, nil
	}

	extraTimeCharge := []sitereportDmn.ExtraTimeChargeItem{}
	err := json.Unmarshal([]byte(param.Report.ExtraTimeCharge), &extraTimeCharge)
	if err != nil {
		return resp, log.LogError(err, nil)
	}

	for _, etc := range extraTimeCharge {
		quantity := etc.TotalWorker
		price := etc.Price

		resp = append(resp, GetDeliverySlipMainTable{
			Item:       fmt.Sprintf("%s(~%s)", ExtraTimeChargeLabel, etc.Time),
			Quantity:   quantity,
			Unit:       UnitPiece,
			Price:      price,
			TotalPrice: float64(quantity) * price,
		})
	}

	return resp, nil
}

func buildLockedDeliverySlipOption(param BuildDeliverySlipParam, resp []GetDeliverySlipMainTable) []GetDeliverySlipMainTable {
	if len(param.Report.Options) == 0 {
		return resp
	}

	for _, opt := range param.Report.Options {
		optSnapshot := sitereportoptionDmn.Snapshot{}
		err := json.Unmarshal([]byte(opt.Snapshot), &optSnapshot)
		if err != nil {
			return resp
		}

		resp = append(resp, GetDeliverySlipMainTable{
			Item:       optSnapshot.Title,
			Quantity:   1,
			Unit:       UnitPiece,
			Price:      optSnapshot.Amount,
			TotalPrice: optSnapshot.Amount,
		})
	}

	return resp
}

func buildUnlockedDeliverySlipBasicPrice(param BuildDeliverySlipParam, resp []GetDeliverySlipMainTable) []GetDeliverySlipMainTable {
	bStartTime := param.Report.BStartTime.Format(time.TimeOnly)[:5] // HH:MM
	bEndTime := param.Report.BEndTime.Format(time.TimeOnly)[:5]     // HH:MM
	quantity := param.Report.Worker
	price := param.CalcResp.ExpensePerWorker
	totalPrice := price * float64(quantity)

	resp = append(resp, GetDeliverySlipMainTable{
		Item:       fmt.Sprintf("%s %s~%s", param.Report.BasicPrice.Title, bStartTime, bEndTime),
		Quantity:   quantity,
		Unit:       UnitPiece,
		Price:      price,
		TotalPrice: totalPrice,
	})

	return resp
}

func buildUnlockedDeliverySlipAddFee(param BuildDeliverySlipParam, resp []GetDeliverySlipMainTable) []GetDeliverySlipMainTable {
	if len(param.Report.DailyReportAdditions) == 0 {
		return resp
	}

	item := AddFeeLabel
	price := param.CalcResp.TotalDailyReportAddition

	for i, dra := range param.Report.DailyReportAdditions {
		item += dra.DailyReportAddition.Title
		if i < len(param.Report.DailyReportAdditions)-1 {
			item += ", "
		}
	}

	resp = append(resp, GetDeliverySlipMainTable{
		Item:       item,
		Quantity:   1,
		Unit:       UnitPiece,
		Price:      price,
		TotalPrice: price,
	})

	return resp
}

func buildUnlockedDeliverySlipDistrictBlock(param BuildDeliverySlipParam, resp []GetDeliverySlipMainTable) []GetDeliverySlipMainTable {
	// block name
	resp = append(resp, GetDeliverySlipMainTable{
		Item:     fmt.Sprintf("%s %s", DistrictBlockNameLabel, param.Report.DistrictBlock.Name),
		Quantity: 1,
		Unit:     UnitPiece,
	})

	// block unit
	price := param.CalcResp.DistrictBlockAmount
	resp = append(resp, GetDeliverySlipMainTable{
		Item:       DistrictBlockUnitLabel,
		Quantity:   param.Report.DistrictBlockUnit,
		Unit:       UnitTravel,
		Price:      price,
		TotalPrice: price,
	})

	return resp
}

func buildUnlockedDeliverySlipTransitPlaceBlock(param BuildDeliverySlipParam, resp []GetDeliverySlipMainTable) []GetDeliverySlipMainTable {
	if param.Report.TransitPlaceBlockID == nil {
		return resp
	}

	// block name
	resp = append(resp, GetDeliverySlipMainTable{
		Item:     fmt.Sprintf("%s %s", TransitPlaceBlockNameLabel, param.Report.TransitPlaceBlock.Name),
		Quantity: 1,
		Unit:     UnitPiece,
	})

	// block unit
	price := param.CalcResp.TransitPlaceBlockAmount
	resp = append(resp, GetDeliverySlipMainTable{
		Item:       TransitPlaceBlockUnitLabel,
		Quantity:   parse.Int64Pointer(param.Report.TransitPlaceUnit),
		Unit:       UnitTravel,
		Price:      price,
		TotalPrice: price,
	})

	return resp
}

func buildUnlockedDeliverySlipExtraTimeCharge(param BuildDeliverySlipParam, resp []GetDeliverySlipMainTable) ([]GetDeliverySlipMainTable, error) {
	if param.Report.ExtraTimeCharge == "[]" {
		return resp, nil
	}

	extraTimeCharge := []SaveSiteReportExtraTimeItem{}
	err := json.Unmarshal([]byte(param.Report.ExtraTimeCharge), &extraTimeCharge)
	if err != nil {
		return resp, log.LogError(err, nil)
	}

	for _, etc := range extraTimeCharge {
		quantity := etc.TotalWorker
		price := param.CalcResp.ExtraTimeChargeData[etc.Time].Price

		resp = append(resp, GetDeliverySlipMainTable{
			Item:       fmt.Sprintf("%s(~%s)", ExtraTimeChargeLabel, etc.Time),
			Quantity:   quantity,
			Unit:       UnitPiece,
			Price:      price,
			TotalPrice: float64(quantity) * price,
		})
	}

	return resp, nil
}

func buildUnlockedDeliverySlipOption(param BuildDeliverySlipParam, resp []GetDeliverySlipMainTable) []GetDeliverySlipMainTable {
	if len(param.Report.Options) == 0 {
		return resp
	}

	for _, opt := range param.Report.Options {
		optData := param.CalcResp.OptionData[opt.OptionID]
		resp = append(resp, GetDeliverySlipMainTable{
			Item:       optData.Title,
			Quantity:   1,
			Unit:       UnitPiece,
			Price:      optData.Amount,
			TotalPrice: optData.Amount,
		})
	}

	return resp
}
